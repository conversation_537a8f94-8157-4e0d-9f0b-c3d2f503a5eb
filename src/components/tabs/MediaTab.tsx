import React, { useState } from "react";
import { Upload, Plus, X } from "lucide-react";

interface AdditionalImage {
  id: number;
  file: File | null;
  preview: string;
}

interface MediaTabProps {
  imageFile: File | null;
  setImageFile: (file: File | null) => void;
}

const MediaTab: React.FC<MediaTabProps> = ({ imageFile, setImageFile }) => {
  const [additionalImages, setAdditionalImages] = useState<AdditionalImage[]>([
    { id: 1, file: null, preview: "" },
    { id: 2, file: null, preview: "" },
    { id: 3, file: null, preview: "" }
  ]);

  // Main image handlers
  const handleMainImageDrop = (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    let file: File | null = null;

    if ('dataTransfer' in e) {
      // This is a drag event
      file = e.dataTransfer?.files[0] || null;
    } else if (e.target && 'files' in e.target) {
      // This is a change event
      file = e.target.files?.[0] || null;
    }

    if (file) {
      setImageFile(file);
    }
  };

  const handleMainImageBrowseClick = () => {
    document.getElementById("main-image-upload")?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  // Additional image handlers
  const handleAdditionalImageChange = (e: React.ChangeEvent<HTMLInputElement>, id: number) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const preview = URL.createObjectURL(file);

      setAdditionalImages(prev =>
        prev.map(img =>
          img.id === id ? { ...img, file, preview } : img
        )
      );
    }
  };

  const handleAdditionalImageBrowseClick = (id: number) => {
    document.getElementById(`additional-image-${id}`)?.click();
  };

  const removeAdditionalImage = (id: number) => {
    setAdditionalImages(prev =>
      prev.map(img =>
        img.id === id ? { ...img, file: null, preview: "" } : img
      )
    );
  };

  const addMoreImageSlot = () => {
    const newId = Math.max(...additionalImages.map(img => img.id)) + 1;
    setAdditionalImages([...additionalImages, { id: newId, file: null, preview: "" }]);
  };

  return (
    <div className="grid grid-cols-1 gap-8">
      {/* Main Image Upload Section */}
      <div className="col-span-1">
        <h3 className="text-lg font-medium text-gray-800 mb-4">รูปภาพหลัก</h3>
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-6 h-80 transition-colors duration-200 hover:border-green-500 bg-gray-50"
          onDragOver={handleDragOver}
          onDrop={handleMainImageDrop}
        >
          {imageFile ? (
            <div className="relative w-full h-full">
              <img
                src={URL.createObjectURL(imageFile)}
                alt="Main Preview"
                className="max-h-full max-w-full object-contain mx-auto"
              />
              <button
                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                onClick={() => setImageFile(null)}
              >
                <X size={16} />
              </button>
            </div>
          ) : (
            <>
              <div className="text-amber-400 mb-4">
                <Upload size={40} />
              </div>
              <p className="text-sm mb-2 text-center">
                Drag and drop an image, or{" "}
                <button
                  onClick={handleMainImageBrowseClick}
                  className="text-green-500 font-semibold hover:text-green-700 transition-colors"
                >
                  Browse
                </button>
              </p>
              <p className="text-xs text-gray-500 text-center">
                Minimum 800px width recommended. Max 10MB each
              </p>
              <input
                id="main-image-upload"
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleMainImageDrop}
              />
            </>
          )}
        </div>
      </div>

      {/* Additional Images Section */}
      <div className="col-span-1">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-800">รูปภาพเพิ่มเติม</h3>
          <button
            onClick={addMoreImageSlot}
            className="flex items-center text-sm text-green-600 hover:text-green-800 transition-colors"
          >
            <Plus size={16} className="mr-1" />
            เพิ่มรูปภาพ
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {additionalImages.map((image) => (
            <div
              key={image.id}
              className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 h-40 transition-colors duration-200 hover:border-green-500 bg-gray-50 relative"
            >
              {image.file ? (
                <>
                  <img
                    src={image.preview}
                    alt={`Additional Image ${image.id}`}
                    className="max-h-full max-w-full object-contain"
                  />
                  <button
                    className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                    onClick={() => removeAdditionalImage(image.id)}
                  >
                    <X size={14} />
                  </button>
                </>
              ) : (
                <>
                  <div className="text-amber-400 mb-2">
                    <Upload size={24} />
                  </div>
                  <p className="text-xs text-gray-500 text-center mb-1">
                    รูปภาพเพิ่มเติม {image.id}
                  </p>
                  <button
                    onClick={() => handleAdditionalImageBrowseClick(image.id)}
                    className="text-xs text-green-500 hover:text-green-700 transition-colors"
                  >
                    เลือกรูปภาพ
                  </button>
                  <input
                    id={`additional-image-${image.id}`}
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={(e) => handleAdditionalImageChange(e, image.id)}
                  />
                </>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Video section */}
      <div className="col-span-1 mt-2">
        <h3 className="text-lg font-medium text-gray-800 mb-4">วีดีโอเพิ่มเติม</h3>
        <div className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-6 h-40 transition-colors duration-200 hover:border-green-500 bg-gray-50">
          <div className="text-amber-400 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-video">
              <path d="m22 8-6 4 6 4V8Z"/>
              <rect width="14" height="12" x="2" y="6" rx="2" ry="2"/>
            </svg>
          </div>
          <p className="text-sm mb-2 text-center">
            เพิ่มวีดีโอหรือลิงก์ YouTube
          </p>
          <input
            type="text"
            placeholder="วางลิงก์ YouTube ที่นี่"
            className="mt-2 w-full max-w-xs text-sm p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
      </div>
    </div>
  );
};

export default MediaTab;
