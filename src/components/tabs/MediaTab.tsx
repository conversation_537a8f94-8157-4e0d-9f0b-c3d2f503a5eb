import React from "react";
import { Upload } from "lucide-react";

interface MediaTabProps {
  imageFile: File | null;
  setImageFile: (file: File | null) => void;
}

const MediaTab: React.FC<MediaTabProps> = ({ imageFile, setImageFile }) => {
  const handleImageDrop = (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    let file: File | null = null;

    if ('dataTransfer' in e) {
      // This is a drag event
      file = e.dataTransfer?.files[0] || null;
    } else if (e.target && 'files' in e.target) {
      // This is a change event
      file = e.target.files?.[0] || null;
    }

    if (file) {
      setImageFile(file);
    }
  };

  const handleBrowseClick = () => {
    document.getElementById("file-upload")?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Image upload */}
      <div className="col-span-2 mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Upload Image
        </label>
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-6 h-80 transition-colors duration-200 hover:border-green-500 bg-gray-50"
          onDragOver={handleDragOver}
          onDrop={handleImageDrop}
        >
          {imageFile ? (
            <div className="relative w-full h-full">
              <img
                src={URL.createObjectURL(imageFile)}
                alt="Preview"
                className="max-h-full max-w-full object-contain mx-auto"
              />
              <button
                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                onClick={() => setImageFile(null)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          ) : (
            <>
              <div className="text-amber-400 mb-4">
                <Upload size={40} />
              </div>
              <p className="text-sm mb-2 text-center">
                Drag and drop an image, or{" "}
                <button
                  onClick={handleBrowseClick}
                  className="text-green-500 font-semibold hover:text-green-700 transition-colors"
                >
                  Browse
                </button>
              </p>
              <p className="text-xs text-gray-500 text-center">
                Minimum 800px width recommended. Max 10MB each
              </p>
              <input
                id="file-upload"
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleImageDrop}
              />
            </>
          )}
        </div>
      </div>
      
      {/* Additional media upload options can be added here */}
      <div className="col-span-2">
        <h3 className="text-lg font-medium text-gray-800 mb-4">รูปภาพเพิ่มเติม</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Placeholder for additional image uploads */}
          {[1, 2, 3].map((index) => (
            <div 
              key={index}
              className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 h-40 transition-colors duration-200 hover:border-green-500 bg-gray-50"
            >
              <div className="text-amber-400 mb-2">
                <Upload size={24} />
              </div>
              <p className="text-xs text-gray-500 text-center">
                เพิ่มรูปภาพที่ {index}
              </p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Video section */}
      <div className="col-span-2 mt-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">วีดีโอเพิ่มเติม</h3>
        <div className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-6 h-40 transition-colors duration-200 hover:border-green-500 bg-gray-50">
          <div className="text-amber-400 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-video">
              <path d="m22 8-6 4 6 4V8Z"/>
              <rect width="14" height="12" x="2" y="6" rx="2" ry="2"/>
            </svg>
          </div>
          <p className="text-sm mb-2 text-center">
            เพิ่มวีดีโอหรือลิงก์ YouTube
          </p>
        </div>
      </div>
    </div>
  );
};

export default MediaTab;
