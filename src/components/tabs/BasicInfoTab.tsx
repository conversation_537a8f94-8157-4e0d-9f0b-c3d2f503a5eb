import React from "react";
import { Link, Plus } from "lucide-react";

interface BasicInfoTabProps {
  storyUrl: string;
  setStoryUrl: (value: string) => void;
  businessType: string;
  setBusinessType: (value: string) => void;
  businessName: string;
  setBusinessName: (value: string) => void;
  shortDescription: string;
  setShortDescription: (value: string) => void;
  contactNumber: string;
  setContactNumber: (value: string) => void;
  social: string;
  setSocial: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({
  storyUrl,
  setStoryUrl,
  businessType,
  setBusinessType,
  businessName,
  setBusinessName,
  shortDescription,
  setShortDescription,
  contactNumber,
  setContactNumber,
  social,
  setSocial,
  description,
  setDescription,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        {/* Story URL */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Story URL (English) <span className="text-red-500">*</span>
          </label>
          <div className="flex relative">
            <div className="absolute left-3 top-3 text-gray-400">
              <Link size={16} />
            </div>
            <input
              type="text"
              placeholder="https://"
              value={storyUrl}
              onChange={(e) => setStoryUrl(e.target.value)}
              className="flex-grow pl-10 border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              required
            />
            <button className="ml-2 p-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Business Type */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ประเภทธุรกิจของคุณ <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <input
              type="text"
              value={businessType}
              onChange={(e) => setBusinessType(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
              required
            />
            <button className="absolute right-2 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
              <Plus size={16} />
            </button>
          </div>
        </div>

        {/* Business Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ชื่อธุรกิจของคุณ <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={businessName}
            onChange={(e) => setBusinessName(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          />
        </div>
      </div>

      <div>
        {/* Short Description */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            คำอธิบายสั้นๆ <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={shortDescription}
            onChange={(e) => setShortDescription(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          />
        </div>

        {/* Contact Number */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            เบอร์ติดต่อ <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={contactNumber}
            onChange={(e) => setContactNumber(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          />
        </div>

        {/* Social */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            โซเชียล <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <input
              type="text"
              value={social}
              onChange={(e) => setSocial(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
              required
            />
            <button className="absolute right-2 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
              <Plus size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Full width textarea */}
      <div className="col-span-2 mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          คำอธิบาย
        </label>
        <textarea
          className="w-full p-3 border border-gray-300 rounded-lg h-40 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
          placeholder="Provide detailed description here..."
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          required
        ></textarea>
      </div>
    </div>
  );
};

export default BasicInfoTab;
