import React from "react";
import { Plus } from "lucide-react";

interface BasicInfoTabProps {
  storyUrl: string;
  setStoryUrl: (value: string) => void;
  businessType: string;
  setBusinessType: (value: string) => void;
  businessName: string;
  setBusinessName: (value: string) => void;
  location: string;
  setLocation: (value: string) => void;
  contactNumber: string;
  setContactNumber: (value: string) => void;
  social: string;
  setSocial: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({
  storyUrl,
  setStoryUrl,
  businessType,
  setBusinessType,
  businessName,
  setBusinessName,
  location,
  setLocation,
  contactNumber,
  setContactNumber,
  social,
  setSocial,
  description,
  setDescription,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        {/* Title */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            หัวข้อ (Title) <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={businessName}
            onChange={(e) => setBusinessName(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            placeholder="ใส่หัวข้อของเนื้อหา"
            required
          />
        </div>

        {/* Date */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            วันที่ (Date) <span className="text-red-500">*</span>
          </label>
          <input
            type="date"
            value={storyUrl}
            onChange={(e) => setStoryUrl(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          />
        </div>

        {/* Category */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            หมวดหมู่ (Category) <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <select
              value={storyUrl}
              onChange={(e) => setStoryUrl(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
              required
            >
              <option value="">เลือกหมวดหมู่</option>
              <option value="restaurant">ร้านอาหาร</option>
              <option value="hotel">โรงแรม</option>
              <option value="attraction">สถานที่ท่องเที่ยว</option>
              <option value="activity">กิจกรรม</option>
              <option value="shopping">ช้อปปิ้ง</option>
              <option value="transport">การเดินทาง</option>
            </select>
            <button className="absolute right-8 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
              <Plus size={16} />
            </button>
          </div>
        </div>

        {/* Status */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            สถานะ (Status) <span className="text-red-500">*</span>
          </label>
          <select
            value={businessType}
            onChange={(e) => setBusinessType(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          >
            <option value="">เลือกสถานะ</option>
            <option value="draft">ร่าง (Draft)</option>
            <option value="published">เผยแพร่ (Published)</option>
            <option value="archived">เก็บถาวร (Archived)</option>
          </select>
        </div>

        {/* Business Type */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ประเภทกิจกรรมกิจกรรมของคุณ <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <input
              type="text"
              value={businessType}
              onChange={(e) => setBusinessType(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
              required
            />
            <button className="absolute right-2 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
              <Plus size={16} />
            </button>
          </div>
        </div>

        {/* Business Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ชื่อกิจกรรมของคุณ<span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={businessName}
            onChange={(e) => setBusinessName(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          />
        </div>
      </div>

      <div>
        {/* Location */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            สถานที่ <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            required
          />
        </div>

        {/* Author */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ผู้เขียน (Author) <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={contactNumber}
            onChange={(e) => setContactNumber(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
            placeholder="ชื่อผู้เขียนเนื้อหา"
            required
          />
        </div>

        {/* Tags */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            แท็ก (Tags)
          </label>
          <div className="relative">
            <input
              type="text"
              value={social}
              onChange={(e) => setSocial(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
              placeholder="เพิ่มแท็ก (คั่นด้วยเครื่องหมายจุลภาค)"
            />
            <button className="absolute right-2 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
              <Plus size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Full width textarea */}
      <div className="col-span-2 mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          คำอธิบายสั้น (Short Description) <span className="text-red-500">*</span>
        </label>
        <textarea
          className="w-full p-3 border border-gray-300 rounded-lg h-32 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
          placeholder="เขียนคำอธิบายสั้นๆ เกี่ยวกับเนื้อหานี้..."
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          maxLength={200}
          required
        ></textarea>
        <div className="text-right text-xs text-gray-500 mt-1">
          {description.length}/200 ตัวอักษร
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
