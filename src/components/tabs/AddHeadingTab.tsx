import { useState } from 'react';
import { Plus, Edit, Trash2, GripVertical } from 'lucide-react';
import Swal from 'sweetalert2';

interface ScheduleItem {
  time: string;
  activity: string;
  description: string;
}

interface PackageItem {
  name: string;
  price: string;
  description: string;
  features: string[];
}

interface Section {
  id: string;
  title: string;
  type: 'description' | 'package-pricing' | 'schedule' | 'image-section' | 'custom';
  content: {
    text?: string;
    html?: string;
    packages?: PackageItem[];
    schedule?: ScheduleItem[];
    images?: File[];
  };
  order: number;
}

const AddHeadingTab = () => {
  const [sections, setSections] = useState<Section[]>([]);
  const [nextSectionNumber, setNextSectionNumber] = useState(1);
  // const [editingSection, setEditingSection] = useState<Section | null>(null);

  const contentTypes = [
    { value: 'description', label: 'คำอธิบาย (Description)', icon: '📝' },
    { value: 'package-pricing', label: 'ราคาแพ็คเกจ (Package Pricing)', icon: '💰' },
    { value: 'schedule', label: 'ตารางเวลา (Schedule)', icon: '📅' },
    { value: 'image-section', label: 'ส่วนรูปภาพ (Image Section)', icon: '🖼️' },
    { value: 'custom', label: 'กำหนดเอง (Custom)', icon: '⚙️' }
  ];

  const addNewSection = async () => {
    const { value: selectedType } = await Swal.fire({
      title: 'เลือกประเภทเนื้อหา',
      text: `สำหรับ Section${nextSectionNumber}`,
      input: 'select',
      inputOptions: contentTypes.reduce((acc, type) => {
        acc[type.value] = `${type.icon} ${type.label}`;
        return acc;
      }, {} as Record<string, string>),
      inputPlaceholder: 'เลือกประเภทเนื้อหา',
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'เพิ่ม',
      cancelButtonText: 'ยกเลิก',
      inputValidator: (value) => {
        if (!value) {
          return 'กรุณาเลือกประเภทเนื้อหา';
        }
      }
    });

    if (selectedType) {
      const newSection: Section = {
        id: `section-${Date.now()}`,
        title: `Section${nextSectionNumber}`,
        type: selectedType as Section['type'],
        content: getDefaultContent(selectedType as Section['type']),
        order: sections.length
      };

      setSections([...sections, newSection]);
      setNextSectionNumber(nextSectionNumber + 1);

      Swal.fire({
        title: 'เพิ่มสำเร็จ!',
        text: `เพิ่ม ${newSection.title} แล้ว`,
        icon: 'success',
        confirmButtonColor: '#10B981',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const getDefaultContent = (type: Section['type']) => {
    switch (type) {
      case 'description':
        return { text: '' };
      case 'package-pricing':
        return { packages: [] };
      case 'schedule':
        return { schedule: [] };
      case 'image-section':
        return { images: [] };
      case 'custom':
        return { html: '' };
      default:
        return {};
    }
  };

  const deleteSection = async (id: string, title: string) => {
    const result = await Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${title} ใช่หรือไม่?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#EF4444',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'ลบ',
      cancelButtonText: 'ยกเลิก'
    });

    if (result.isConfirmed) {
      setSections(sections.filter(section => section.id !== id));
      Swal.fire({
        title: 'ลบสำเร็จ!',
        text: `ลบ ${title} แล้ว`,
        icon: 'success',
        confirmButtonColor: '#10B981',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const getTypeLabel = (type: Section['type']) => {
    const typeInfo = contentTypes.find(t => t.value === type);
    return typeInfo ? typeInfo.label : type;
  };

  const getTypeIcon = (type: Section['type']) => {
    const typeInfo = contentTypes.find(t => t.value === type);
    return typeInfo ? typeInfo.icon : '📄';
  };

  const editSection = async (section: Section) => {
    // setEditingSection(section);

    if (section.type === 'schedule') {
      await editScheduleSection(section);
    } else if (section.type === 'package-pricing') {
      await editPackagePricingSection(section);
    } else if (section.type === 'description') {
      await editDescriptionSection(section);
    } else {
      // For other types, show a simple text editor for now
      await editGenericSection(section);
    }
  };

  const editScheduleSection = async (section: Section) => {
    const currentSchedule = section.content.schedule || [];

    const { value: formValues } = await Swal.fire({
      title: `แก้ไข ${section.title} - กำหนดการ`,
      html: `
        <div class="text-left">
          <div id="schedule-items">
            ${currentSchedule.map((item: ScheduleItem, index: number) => `
              <div class="mb-4 p-4 border rounded-lg" data-index="${index}">
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">เวลา:</label>
                  <input type="text" class="w-full p-2 border rounded" value="${item.time}" data-field="time" data-index="${index}">
                </div>
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">กิจกรรม:</label>
                  <input type="text" class="w-full p-2 border rounded" value="${item.activity}" data-field="activity" data-index="${index}">
                </div>
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">รายละเอียด:</label>
                  <textarea class="w-full p-2 border rounded" rows="2" data-field="description" data-index="${index}">${item.description}</textarea>
                </div>
                <button type="button" class="text-red-600 text-sm" onclick="removeScheduleItem(${index})">ลบรายการ</button>
              </div>
            `).join('')}
          </div>
          <button type="button" id="add-schedule-item" class="mt-2 px-4 py-2 bg-green-600 text-white rounded">เพิ่มรายการ</button>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'บันทึก',
      cancelButtonText: 'ยกเลิก',
      width: '600px',
      didOpen: () => {
        // Add event listeners for dynamic functionality
        const addButton = document.getElementById('add-schedule-item');
        addButton?.addEventListener('click', () => {
          const container = document.getElementById('schedule-items');
          const newIndex = container?.children.length || 0;
          const newItem = document.createElement('div');
          newItem.className = 'mb-4 p-4 border rounded-lg';
          newItem.setAttribute('data-index', newIndex.toString());
          newItem.innerHTML = `
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">เวลา:</label>
              <input type="text" class="w-full p-2 border rounded" value="" data-field="time" data-index="${newIndex}">
            </div>
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">กิจกรรม:</label>
              <input type="text" class="w-full p-2 border rounded" value="" data-field="activity" data-index="${newIndex}">
            </div>
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">รายละเอียด:</label>
              <textarea class="w-full p-2 border rounded" rows="2" data-field="description" data-index="${newIndex}"></textarea>
            </div>
            <button type="button" class="text-red-600 text-sm" onclick="this.parentElement.remove()">ลบรายการ</button>
          `;
          container?.appendChild(newItem);
        });
      },
      preConfirm: () => {
        const scheduleItems: ScheduleItem[] = [];
        const container = document.getElementById('schedule-items');
        const items = container?.children;

        if (items) {
          for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const time = (item.querySelector('[data-field="time"]') as HTMLInputElement)?.value || '';
            const activity = (item.querySelector('[data-field="activity"]') as HTMLInputElement)?.value || '';
            const description = (item.querySelector('[data-field="description"]') as HTMLTextAreaElement)?.value || '';

            if (time && activity) {
              scheduleItems.push({ time, activity, description });
            }
          }
        }

        return scheduleItems;
      }
    });

    if (formValues) {
      const updatedSections = sections.map(s =>
        s.id === section.id
          ? { ...s, content: { ...s.content, schedule: formValues } }
          : s
      );
      setSections(updatedSections);

      Swal.fire({
        title: 'บันทึกสำเร็จ!',
        text: 'อัปเดตกำหนดการแล้ว',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const editDescriptionSection = async (section: Section) => {
    const { value: text } = await Swal.fire({
      title: `แก้ไข ${section.title} - คำอธิบาย`,
      input: 'textarea',
      inputValue: section.content.text || '',
      inputPlaceholder: 'เขียนคำอธิบายที่นี่...',
      showCancelButton: true,
      confirmButtonText: 'บันทึก',
      cancelButtonText: 'ยกเลิก',
      inputValidator: (value) => {
        if (!value) {
          return 'กรุณาใส่คำอธิบาย';
        }
      }
    });

    if (text) {
      const updatedSections = sections.map(s =>
        s.id === section.id
          ? { ...s, content: { ...s.content, text } }
          : s
      );
      setSections(updatedSections);

      Swal.fire({
        title: 'บันทึกสำเร็จ!',
        text: 'อัปเดตคำอธิบายแล้ว',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const editPackagePricingSection = async (section: Section) => {
    const currentPackages = section.content.packages || [];

    const { value: formValues } = await Swal.fire({
      title: `แก้ไข ${section.title} - ราคาแพ็คเกจ`,
      html: `
        <div class="text-left">
          <div id="package-items">
            ${currentPackages.map((pkg: PackageItem, index: number) => `
              <div class="mb-4 p-4 border rounded-lg" data-index="${index}">
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">ชื่อแพ็คเกจ:</label>
                  <input type="text" class="w-full p-2 border rounded" value="${pkg.name}" data-field="name" data-index="${index}">
                </div>
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">ราคา:</label>
                  <input type="text" class="w-full p-2 border rounded" value="${pkg.price}" data-field="price" data-index="${index}">
                </div>
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">รายละเอียด:</label>
                  <textarea class="w-full p-2 border rounded" rows="2" data-field="description" data-index="${index}">${pkg.description}</textarea>
                </div>
                <div class="mb-2">
                  <label class="block text-sm font-medium mb-1">คุณสมบัติ (แยกด้วยเครื่องหมายจุลภาค):</label>
                  <input type="text" class="w-full p-2 border rounded" value="${pkg.features?.join(', ') || ''}" data-field="features" data-index="${index}">
                </div>
                <button type="button" class="text-red-600 text-sm" onclick="this.parentElement.remove()">ลบแพ็คเกจ</button>
              </div>
            `).join('')}
          </div>
          <button type="button" id="add-package-item" class="mt-2 px-4 py-2 bg-green-600 text-white rounded">เพิ่มแพ็คเกจ</button>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'บันทึก',
      cancelButtonText: 'ยกเลิก',
      width: '600px',
      didOpen: () => {
        const addButton = document.getElementById('add-package-item');
        addButton?.addEventListener('click', () => {
          const container = document.getElementById('package-items');
          const newIndex = container?.children.length || 0;
          const newItem = document.createElement('div');
          newItem.className = 'mb-4 p-4 border rounded-lg';
          newItem.setAttribute('data-index', newIndex.toString());
          newItem.innerHTML = `
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">ชื่อแพ็คเกจ:</label>
              <input type="text" class="w-full p-2 border rounded" value="" data-field="name" data-index="${newIndex}">
            </div>
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">ราคา:</label>
              <input type="text" class="w-full p-2 border rounded" value="" data-field="price" data-index="${newIndex}">
            </div>
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">รายละเอียด:</label>
              <textarea class="w-full p-2 border rounded" rows="2" data-field="description" data-index="${newIndex}"></textarea>
            </div>
            <div class="mb-2">
              <label class="block text-sm font-medium mb-1">คุณสมบัติ (แยกด้วยเครื่องหมายจุลภาค):</label>
              <input type="text" class="w-full p-2 border rounded" value="" data-field="features" data-index="${newIndex}">
            </div>
            <button type="button" class="text-red-600 text-sm" onclick="this.parentElement.remove()">ลบแพ็คเกจ</button>
          `;
          container?.appendChild(newItem);
        });
      },
      preConfirm: () => {
        const packageItems: PackageItem[] = [];
        const container = document.getElementById('package-items');
        const items = container?.children;

        if (items) {
          for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const name = (item.querySelector('[data-field="name"]') as HTMLInputElement)?.value || '';
            const price = (item.querySelector('[data-field="price"]') as HTMLInputElement)?.value || '';
            const description = (item.querySelector('[data-field="description"]') as HTMLTextAreaElement)?.value || '';
            const featuresStr = (item.querySelector('[data-field="features"]') as HTMLInputElement)?.value || '';
            const features = featuresStr.split(',').map(f => f.trim()).filter(f => f);

            if (name && price) {
              packageItems.push({ name, price, description, features });
            }
          }
        }

        return packageItems;
      }
    });

    if (formValues) {
      const updatedSections = sections.map(s =>
        s.id === section.id
          ? { ...s, content: { ...s.content, packages: formValues } }
          : s
      );
      setSections(updatedSections);

      Swal.fire({
        title: 'บันทึกสำเร็จ!',
        text: 'อัปเดตราคาแพ็คเกจแล้ว',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const editGenericSection = async (section: Section) => {
    const { value: html } = await Swal.fire({
      title: `แก้ไข ${section.title}`,
      input: 'textarea',
      inputValue: section.content.html || section.content.text || '',
      inputPlaceholder: 'เขียนเนื้อหาที่นี่...',
      showCancelButton: true,
      confirmButtonText: 'บันทึก',
      cancelButtonText: 'ยกเลิก'
    });

    if (html !== undefined) {
      const updatedSections = sections.map(s =>
        s.id === section.id
          ? { ...s, content: { ...s.content, html } }
          : s
      );
      setSections(updatedSections);

      Swal.fire({
        title: 'บันทึกสำเร็จ!',
        text: 'อัปเดตเนื้อหาแล้ว',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">จัดการหัวข้อเนื้อหา</h2>
          <p className="text-sm text-gray-500 mt-1">
            เพิ่มและจัดการส่วนต่างๆ ของเนื้อหา เช่น POST1, POST2, POST3
          </p>
        </div>
        <button
          onClick={addNewSection}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus size={20} className="mr-2" />
          เพิ่มหัวข้อใหม่
        </button>
      </div>

      {/* Sections List */}
      {sections.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-gray-400 mb-4">
            <Plus size={48} className="mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-500 mb-2">ยังไม่มีหัวข้อเนื้อหา</h3>
          <p className="text-gray-400 mb-4">เริ่มต้นด้วยการเพิ่มหัวข้อแรกของคุณ</p>
          <button
            onClick={addNewSection}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            เพิ่มหัวข้อแรก
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {sections.map((section) => (
            <div
              key={section.id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center text-gray-400 cursor-move">
                    <GripVertical size={20} />
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getTypeIcon(section.type)}</span>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{section.title}</h3>
                      <p className="text-sm text-gray-500">{getTypeLabel(section.type)}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => editSection(section)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="แก้ไข"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => deleteSection(section.id, section.title)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="ลบ"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>

              {/* Content Preview */}
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                {section.type === 'schedule' && section.content.schedule && section.content.schedule.length > 0 ? (
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">กำหนดการ</h4>
                    <div className="space-y-2">
                      {section.content.schedule.slice(0, 2).map((item: ScheduleItem, index: number) => (
                        <div key={index} className="flex border-l-4 border-green-700 pl-3 text-sm">
                          <div className="w-20 font-medium text-gray-700">{item.time}</div>
                          <div>
                            <div className="font-medium">{item.activity}</div>
                            <div className="text-gray-600 text-xs">{item.description}</div>
                          </div>
                        </div>
                      ))}
                      {section.content.schedule.length > 2 && (
                        <p className="text-xs text-gray-500">และอีก {section.content.schedule.length - 2} รายการ...</p>
                      )}
                    </div>
                  </div>
                ) : section.type === 'package-pricing' && section.content.packages && section.content.packages.length > 0 ? (
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">แพ็คเกจ</h4>
                    <div className="space-y-2">
                      {section.content.packages.slice(0, 2).map((pkg: PackageItem, index: number) => (
                        <div key={index} className="border rounded p-2 text-sm">
                          <div className="font-medium">{pkg.name} - {pkg.price}</div>
                          <div className="text-gray-600 text-xs">{pkg.description}</div>
                        </div>
                      ))}
                      {section.content.packages.length > 2 && (
                        <p className="text-xs text-gray-500">และอีก {section.content.packages.length - 2} แพ็คเกจ...</p>
                      )}
                    </div>
                  </div>
                ) : section.type === 'description' && section.content.text ? (
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">คำอธิบาย</h4>
                    <p className="text-sm text-gray-600 line-clamp-3">{section.content.text}</p>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">ยังไม่มีเนื้อหา</p>
                    <button
                      onClick={() => editSection(section)}
                      className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                    >
                      คลิกเพื่อเพิ่มเนื้อหา
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Summary */}
      {sections.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2">สรุป</h4>
          <p className="text-sm text-blue-700">
            มีหัวข้อเนื้อหาทั้งหมด {sections.length} หัวข้อ
          </p>
          <div className="mt-2 flex flex-wrap gap-2">
            {sections.map((section) => (
              <span
                key={section.id}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {getTypeIcon(section.type)} {section.title}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AddHeadingTab;