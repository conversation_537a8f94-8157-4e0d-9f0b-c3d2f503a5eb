import { useState } from 'react';
import { Plus, Edit, Trash2, GripVertical } from 'lucide-react';
import Swal from 'sweetalert2';
import React from 'react';

interface PostSection {
  id: string;
  title: string;
  type: 'description' | 'package-pricing' | 'schedule' | 'image-section' | 'custom';
  content: any;
  order: number;
}

const AddHeadingTab = () => {
  const [postSections, setPostSections] = useState<PostSection[]>([]);
  const [nextPostNumber, setNextPostNumber] = useState(1);

  const contentTypes = [
    { value: 'description', label: 'คำอธิบาย (Description)', icon: '📝' },
    { value: 'package-pricing', label: 'ราคาแพ็คเกจ (Package Pricing)', icon: '💰' },
    { value: 'schedule', label: 'ตารางเวลา (Schedule)', icon: '📅' },
    { value: 'image-section', label: 'ส่วนรูปภาพ (Image Section)', icon: '🖼️' },
    { value: 'custom', label: 'กำหนดเอง (Custom)', icon: '⚙️' }
  ];

  const addNewPostSection = async () => {
    const { value: selectedType } = await Swal.fire({
      title: 'เลือกประเภทเนื้อหา',
      text: `สำหรับ POST${nextPostNumber}`,
      input: 'select',
      inputOptions: contentTypes.reduce((acc, type) => {
        acc[type.value] = `${type.icon} ${type.label}`;
        return acc;
      }, {} as Record<string, string>),
      inputPlaceholder: 'เลือกประเภทเนื้อหา',
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'เพิ่ม',
      cancelButtonText: 'ยกเลิก',
      inputValidator: (value) => {
        if (!value) {
          return 'กรุณาเลือกประเภทเนื้อหา';
        }
      }
    });

    if (selectedType) {
      const newSection: PostSection = {
        id: `post-${Date.now()}`,
        title: `POST${nextPostNumber}`,
        type: selectedType as PostSection['type'],
        content: getDefaultContent(selectedType as PostSection['type']),
        order: postSections.length
      };

      setPostSections([...postSections, newSection]);
      setNextPostNumber(nextPostNumber + 1);

      Swal.fire({
        title: 'เพิ่มสำเร็จ!',
        text: `เพิ่ม ${newSection.title} แล้ว`,
        icon: 'success',
        confirmButtonColor: '#10B981',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const getDefaultContent = (type: PostSection['type']) => {
    switch (type) {
      case 'description':
        return { text: '' };
      case 'package-pricing':
        return { packages: [] };
      case 'schedule':
        return { schedules: [] };
      case 'image-section':
        return { images: [] };
      case 'custom':
        return { html: '' };
      default:
        return {};
    }
  };

  const deletePostSection = async (id: string, title: string) => {
    const result = await Swal.fire({
      title: 'ยืนยันการลบ',
      text: `คุณต้องการลบ ${title} ใช่หรือไม่?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#EF4444',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'ลบ',
      cancelButtonText: 'ยกเลิก'
    });

    if (result.isConfirmed) {
      setPostSections(postSections.filter(section => section.id !== id));
      Swal.fire({
        title: 'ลบสำเร็จ!',
        text: `ลบ ${title} แล้ว`,
        icon: 'success',
        confirmButtonColor: '#10B981',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const getTypeLabel = (type: PostSection['type']) => {
    const typeInfo = contentTypes.find(t => t.value === type);
    return typeInfo ? typeInfo.label : type;
  };

  const getTypeIcon = (type: PostSection['type']) => {
    const typeInfo = contentTypes.find(t => t.value === type);
    return typeInfo ? typeInfo.icon : '📄';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">จัดการหัวข้อเนื้อหา</h2>
          <p className="text-sm text-gray-500 mt-1">
            เพิ่มและจัดการส่วนต่างๆ ของเนื้อหา เช่น POST1, POST2, POST3
          </p>
        </div>
        <button
          onClick={addNewPostSection}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus size={20} className="mr-2" />
          เพิ่มหัวข้อใหม่
        </button>
      </div>

      {/* POST Sections List */}
      {postSections.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-gray-400 mb-4">
            <Plus size={48} className="mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-500 mb-2">ยังไม่มีหัวข้อเนื้อหา</h3>
          <p className="text-gray-400 mb-4">เริ่มต้นด้วยการเพิ่มหัวข้อแรกของคุณ</p>
          <button
            onClick={addNewPostSection}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            เพิ่มหัวข้อแรก
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {postSections.map((section) => (
            <div
              key={section.id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center text-gray-400 cursor-move">
                    <GripVertical size={20} />
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getTypeIcon(section.type)}</span>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{section.title}</h3>
                      <p className="text-sm text-gray-500">{getTypeLabel(section.type)}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="แก้ไข"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => deletePostSection(section.id, section.title)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="ลบ"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>

              {/* Content Preview */}
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  เนื้อหาของ {section.title} จะแสดงที่นี่
                </p>
                {/* Here you would render different content based on section.type */}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Summary */}
      {postSections.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2">สรุป</h4>
          <p className="text-sm text-blue-700">
            มีหัวข้อเนื้อหาทั้งหมด {postSections.length} หัวข้อ
          </p>
          <div className="mt-2 flex flex-wrap gap-2">
            {postSections.map((section) => (
              <span
                key={section.id}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {getTypeIcon(section.type)} {section.title}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AddHeadingTab;