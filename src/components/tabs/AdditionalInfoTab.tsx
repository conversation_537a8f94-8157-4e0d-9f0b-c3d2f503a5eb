import React from "react";
import { ChevronDown } from "lucide-react";
import { Amenity } from "../AmenitiesManagement";

interface AdditionalInfoTabProps {
  reference: string;
  setReference: (value: string) => void;
  imageSource: string;
  setImageSource: (value: string) => void;
  imageDescription: string;
  setImageDescription: (value: string) => void;
  highlights: string;
  setHighlights: (value: string) => void;
  selectedAmenities: number[];
  amenities: Amenity[];
  handleAmenityToggle: (amenityId: number) => void;
}

const AdditionalInfoTab: React.FC<AdditionalInfoTabProps> = ({
  reference,
  setReference,
  imageSource,
  setImageSource,
  imageDescription,
  setImageDescription,
  highlights,
  setHighlights,
  selectedAmenities,
  amenities,
  handleAmenityToggle,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Amenities Checkboxes */}
      <div className="col-span-2 mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-4">
          สิ่งอำนวยความสะดวก
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {amenities.map((amenity) => (
            <div key={amenity.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <input
                type="checkbox"
                id={`amenity-${amenity.id}`}
                checked={selectedAmenities.includes(amenity.id)}
                onChange={() => handleAmenityToggle(amenity.id)}
                className="h-5 w-5 text-green-600 rounded border-gray-300 focus:ring-green-500"
              />
              <label htmlFor={`amenity-${amenity.id}`} className="flex flex-col cursor-pointer">
                <span className="font-medium text-gray-700">{amenity.name}</span>
                <span className="text-xs text-gray-500">{amenity.description}</span>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Additional fields */}
      <div className="col-span-1 space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            อ้างอิง
          </label>
          <input
            type="text"
            value={reference}
            onChange={(e) => setReference(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ที่มาของภาพอธิบาย
          </label>
          <input
            type="text"
            value={imageSource}
            onChange={(e) => setImageSource(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
          />
        </div>
      </div>

      <div className="col-span-1 space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            คำอธิบายภาพประกอบบทความ
          </label>
          <input
            type="text"
            value={imageDescription}
            onChange={(e) => setImageDescription(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Highlights
          </label>
          <div className="border border-gray-300 rounded-lg relative">
            <textarea
              className="w-full p-3 focus:outline-none text-sm h-32 rounded-lg focus:ring-2 focus:ring-green-500 transition-all"
              placeholder="Add highlights here..."
              value={highlights}
              onChange={(e) => setHighlights(e.target.value)}
            ></textarea>
            <button className="absolute right-3 bottom-3 text-gray-400 hover:text-gray-600 transition-colors">
              <ChevronDown size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfoTab;
