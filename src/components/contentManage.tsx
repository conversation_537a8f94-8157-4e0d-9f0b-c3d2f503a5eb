import React, { useState, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
// import { Amenity } from "./AmenitiesManagement";
import BasicInfoTab from "./tabs/BasicInfoTab";
import AddHeadingTab from "./tabs/AddHeadingTab";

// This interface would be used in a full implementation
// interface AdditionalImage {
//   id: number;
//   file: File | null;
//   preview: string;
// }

const ContentManage: React.FC = () => {
  const navigate = useNavigate();
  // These states would be used in a full implementation to store additional media
  // const [imageFile, setImageFile] = useState<File | null>(null);
  // const [additionalImages, setAdditionalImages] = useState<AdditionalImage[]>([]);
  // const [videoUrl, setVideoUrl] = useState("");
  const [activeTab, setActiveTab] = useState<"general" | "heading">("general");
  const [businessName, setBusinessName] = useState("");
  const [businessType, setBusinessType] = useState("");
  const [location, setLocation] = useState("");
  const [contactNumber, setContactNumber] = useState("");
  const [social, setSocial] = useState("");
  const [description, setDescription] = useState("");
  const [storyUrl, setStoryUrl] = useState("");
  const [reference, setReference] = useState("");
  const [imageSource, setImageSource] = useState("");
  const [imageDescription, setImageDescription] = useState("");
  const [highlights, setHighlights] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  // These would be used for amenities in a full implementation
  // const [selectedAmenities, setSelectedAmenities] = useState<number[]>([]);
  // const [amenities] = useState<Amenity[]>([...]);

  // Get the ID from URL params
  const { id } = useParams();
  const routerLocation = useLocation();

  useEffect(() => {
    // Check if we're in edit mode (URL has an ID parameter)
    if (id) {
      setIsEditMode(true);

      // If state was passed through navigation
      if (routerLocation.state && routerLocation.state.storeData) {
        const storeData = routerLocation.state.storeData;

        // Populate form fields with the store data
        setBusinessName(storeData.title || "");
        setBusinessType(storeData.businessType || "");
        setLocation(storeData.location || "");
        setContactNumber(storeData.contactNumber || "");
        setSocial(storeData.social || "");
        setDescription(storeData.description || "");
        setStoryUrl(storeData.storyUrl || "");
        setReference(storeData.reference || "");
        setImageSource(storeData.imageSource || "");
        setImageDescription(storeData.imageDescription || "");
        setHighlights(storeData.highlights || "");
        // setSelectedAmenities(storeData.amenities || []);
      } else {
        // If no state was passed, you could fetch the data from an API using the ID
        console.log(`Would fetch data for store with ID: ${id}`);
      }
    }
  }, [id, routerLocation]);

  // Amenity toggle handler would be here in a full implementation
  // const handleAmenityToggle = (amenityId: number) => { ... };

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="max-w-5xl mx-auto p-6 bg-white rounded-xl shadow-lg">
        <div className="border-b border-gray-200 pb-4 mb-6">
          <h1 className="text-3xl font-bold text-gray-800">{isEditMode ? businessName || "แก้ไขข้อมูลธุรกิจ" : "เพิ่มข้อมูลธุรกิจใหม่"}</h1>
          <p className="text-gray-500 mt-1">
            {isEditMode ? "แก้ไขข้อมูลของธุรกิจในระบบของคุณที่นี่" : "เพิ่มข้อมูลของธุรกิจใหม่ในระบบของคุณที่นี่"}
          </p>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            className={`px-4 py-2 mr-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === "general"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("general")}
          >
            ข้อมูลพื้นฐาน
          </button>
          <button
            className={`px-4 py-2 mr-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === "heading"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("heading")}
          >
            เพิ่มหัวข้อ
          </button>
        </div>

        {/* Basic Info Tab Content - Required Data */}
        {activeTab === "general" && (
          <BasicInfoTab
            storyUrl={storyUrl}
            setStoryUrl={setStoryUrl}
            businessType={businessType}
            setBusinessType={setBusinessType}
            businessName={businessName}
            setBusinessName={setBusinessName}
            location={location}
            setLocation={setLocation}
            contactNumber={contactNumber}
            setContactNumber={setContactNumber}
            social={social}
            setSocial={setSocial}
            description={description}
            setDescription={setDescription}
          />
        )}

        {/* Add Heading Tab Content - Dynamic POST Sections */}
        {activeTab === "heading" && (
          <AddHeadingTab />
        )}

        {/* Submit button */}
        <div className="flex justify-end mt-6">
          <button
            className="bg-gray-300 text-gray-700 py-2 px-4 rounded-lg shadow hover:bg-gray-400 transition duration-200 mr-3"
            onClick={() => navigate(-1)}
          >
            Cancel
          </button>
          <button
            className="bg-green-600 text-white py-2 px-6 rounded-lg shadow-md hover:bg-green-700 transition duration-200"
            onClick={() => {
              // Validate required fields
              if (!businessName || !businessType || !location || !contactNumber || !social || !storyUrl) {
                Swal.fire({
                  title: 'Missing Information',
                  text: 'Please fill in all required fields marked with *',
                  icon: 'warning',
                  confirmButtonColor: '#10B981',
                });
                return;
              }

              // Show confirmation dialog
              Swal.fire({
                title: isEditMode ? 'Update Business Information?' : 'Submit New Business?',
                html: `
                  <div class="text-left mb-4">
                    <p class="font-medium text-gray-700">${businessName}</p>
                    <p class="text-sm text-gray-500">${businessType}</p>
                    <p class="text-sm text-gray-500">${location}</p>
                  </div>
                  <p class="mb-4">Are you sure you want to ${isEditMode ? 'update' : 'submit'} this business information?</p>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#10B981',
                cancelButtonColor: '#6B7280',
                confirmButtonText: isEditMode ? 'Update' : 'Submit',
                cancelButtonText: 'Cancel',
              }).then((result) => {
                if (result.isConfirmed) {
                  // Handle form submission
                  // In a real app, you would send the data to your backend here
                  console.log('Form submitted:', {
                    businessName,
                    businessType,
                    location,
                    contactNumber,
                    social,
                    description,
                    storyUrl,
                    reference,
                    imageSource,
                    imageDescription,
                    highlights,
                    // selectedAmenities,
                    // Additional images and video would be included here in a real implementation
                  });

                  // Show success message
                  Swal.fire({
                    title: isEditMode ? 'Updated!' : 'Submitted!',
                    text: isEditMode
                      ? `${businessName} has been updated successfully.`
                      : `${businessName} has been submitted successfully.`,
                    icon: 'success',
                    confirmButtonColor: '#10B981',
                  }).then(() => {
                    // Navigate back to the stores page
                    navigate('/stores');
                  });
                }
              });
            }}
          >
            {isEditMode ? "Update" : "Submit"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContentManage;