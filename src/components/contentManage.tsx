import React, { useState, useEffect } from "react";
import { ChevronDown, Upload, Link, Plus, Check } from "lucide-react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { Amenity } from "./AmenitiesManagement";

const ContentManage: React.FC = () => {
  const navigate = useNavigate();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [activeTab, setActiveTab] = useState<"general" | "media" | "additional">("general");
  const [businessName, setBusinessName] = useState("");
  const [businessType, setBusinessType] = useState("");
  const [shortDescription, setShortDescription] = useState("");
  const [contactNumber, setContactNumber] = useState("");
  const [social, setSocial] = useState("");
  const [description, setDescription] = useState("");
  const [storyUrl, setStoryUrl] = useState("");
  const [reference, setReference] = useState("");
  const [imageSource, setImageSource] = useState("");
  const [imageDescription, setImageDescription] = useState("");
  const [highlights, setHighlights] = useState("");
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedAmenities, setSelectedAmenities] = useState<number[]>([]);

  // Sample amenities data (in a real app, this would come from an API)
  const [amenities] = useState<Amenity[]>([
    {
      id: 1,
      name: "Wi-Fi",
      description: "บริการอินเทอร์เน็ตไร้สาย",
      icon: "wifi",
      createdAt: "2023-05-15",
    },
    {
      id: 2,
      name: "ที่จอดรถ",
      description: "บริการที่จอดรถฟรี",
      icon: "car",
      createdAt: "2023-05-16",
    },
    {
      id: 3,
      name: "อาหารเช้า",
      description: "บริการอาหารเช้า",
      icon: "coffee",
      createdAt: "2023-05-17",
    },
    {
      id: 4,
      name: "สระว่ายน้ำ",
      description: "สระว่ายน้ำส่วนกลาง",
      icon: "water",
      createdAt: "2023-05-18",
    },
  ]);

  // Get the ID from URL params
  const { id } = useParams();
  const location = useLocation();

  useEffect(() => {
    // Check if we're in edit mode (URL has an ID parameter)
    if (id) {
      setIsEditMode(true);

      // If state was passed through navigation
      if (location.state && location.state.storeData) {
        const storeData = location.state.storeData;

        // Populate form fields with the store data
        setBusinessName(storeData.title || "");
        setBusinessType(storeData.businessType || "");
        setShortDescription(storeData.shortDescription || "");
        setContactNumber(storeData.contactNumber || "");
        setSocial(storeData.social || "");
        setDescription(storeData.description || "");
        setStoryUrl(storeData.storyUrl || "");
        setReference(storeData.reference || "");
        setImageSource(storeData.imageSource || "");
        setImageDescription(storeData.imageDescription || "");
        setHighlights(storeData.highlights || "");
        setSelectedAmenities(storeData.amenities || []);
      } else {
        // If no state was passed, you could fetch the data from an API using the ID
        console.log(`Would fetch data for store with ID: ${id}`);
      }
    }
  }, [id, location]);

  const handleImageDrop = (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    let file: File | null = null;

    if ('dataTransfer' in e) {
      // This is a drag event
      file = e.dataTransfer?.files[0] || null;
    } else if (e.target && 'files' in e.target) {
      // This is a change event
      file = e.target.files?.[0] || null;
    }

    if (file) {
      setImageFile(file);
    }
  };

  const handleBrowseClick = () => {
    document.getElementById("file-upload")?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleAmenityToggle = (amenityId: number) => {
    setSelectedAmenities(prev => {
      if (prev.includes(amenityId)) {
        return prev.filter(id => id !== amenityId);
      } else {
        return [...prev, amenityId];
      }
    });
  };

  return (
    <div className="bg-gray-50 min-h-screen py-8">
      <div className="max-w-5xl mx-auto p-6 bg-white rounded-xl shadow-lg">
        <div className="border-b border-gray-200 pb-4 mb-6">
          <h1 className="text-3xl font-bold text-gray-800">{isEditMode ? businessName || "แก้ไขข้อมูลธุรกิจ" : "เพิ่มข้อมูลธุรกิจใหม่"}</h1>
          <p className="text-gray-500 mt-1">
            {isEditMode ? "แก้ไขข้อมูลของธุรกิจในระบบของคุณที่นี่" : "เพิ่มข้อมูลของธุรกิจใหม่ในระบบของคุณที่นี่"}
          </p>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            className={`px-4 py-2 mr-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === "general"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("general")}
          >
            ข้อมูลพื้นฐาน
          </button>
          <button
            className={`px-4 py-2 mr-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === "media"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("media")}
          >
            รูปภาพ/วีดีโอเพิ่มเติม
          </button>
          <button
            className={`px-4 py-2 mr-2 font-medium text-sm transition-colors duration-200 ${
              activeTab === "additional"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("additional")}
          >
            ข้อมูลเพิ่มเติม
          </button>
        </div>

        {/* General Tab Content */}
        {activeTab === "general" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              {/* Story URL */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Story URL (English) <span className="text-red-500">*</span>
                </label>
                <div className="flex relative">
                  <div className="absolute left-3 top-3 text-gray-400">
                    <Link size={16} />
                  </div>
                  <input
                    type="text"
                    placeholder="https://"
                    value={storyUrl}
                    onChange={(e) => setStoryUrl(e.target.value)}
                    className="flex-grow pl-10 border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                    required
                  />
                  <button className="ml-2 p-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Business Type */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ประเภทธุรกิจของคุณ <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={businessType}
                    onChange={(e) => setBusinessType(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                    required
                  />
                  <button className="absolute right-2 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
                    <Plus size={16} />
                  </button>
                </div>
              </div>

              {/* Business Name */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ชื่อธุรกิจของคุณ <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={businessName}
                  onChange={(e) => setBusinessName(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                  required
                />
              </div>
            </div>

            <div>
              {/* Short Description */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  คำอธิบายสั้นๆ <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={shortDescription}
                  onChange={(e) => setShortDescription(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                  required
                />
              </div>

              {/* Contact Number */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  เบอร์ติดต่อ <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={contactNumber}
                  onChange={(e) => setContactNumber(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                  required
                />
              </div>

              {/* Social */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  โซเชียล <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={social}
                    onChange={(e) => setSocial(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                    required
                  />
                  <button className="absolute right-2 top-2 p-1 bg-green-500 text-white rounded-full cursor-pointer hover:bg-green-600 transition-colors">
                    <Plus size={16} />
                  </button>
                </div>
              </div>
            </div>

            {/* Full width textarea */}
            <div className="col-span-2 mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                คำอธิบาย
              </label>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-lg h-40 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                placeholder="Provide detailed description here..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
              ></textarea>
            </div>
          </div>
        )}

        {/* Media Tab Content */}
        {activeTab === "media" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Image upload */}
            <div className="col-span-2 mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Image
              </label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-6 h-80 transition-colors duration-200 hover:border-green-500 bg-gray-50"
                onDragOver={handleDragOver}
                onDrop={handleImageDrop}
              >
                {imageFile ? (
                  <div className="relative w-full h-full">
                    <img
                      src={URL.createObjectURL(imageFile)}
                      alt="Preview"
                      className="max-h-full max-w-full object-contain mx-auto"
                    />
                    <button
                      className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                      onClick={() => setImageFile(null)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <>
                    <div className="text-amber-400 mb-4">
                      <Upload size={40} />
                    </div>
                    <p className="text-sm mb-2 text-center">
                      Drag and drop an image, or{" "}
                      <button
                        onClick={handleBrowseClick}
                        className="text-green-500 font-semibold hover:text-green-700 transition-colors"
                      >
                        Browse
                      </button>
                    </p>
                    <p className="text-xs text-gray-500 text-center">
                      Minimum 800px width recommended. Max 10MB each
                    </p>
                    <input
                      id="file-upload"
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleImageDrop}
                    />
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Additional Tab Content */}
        {activeTab === "additional" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Amenities Checkboxes */}
            <div className="col-span-2 mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-4">
                สิ่งอำนวยความสะดวก
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {amenities.map((amenity) => (
                  <div key={amenity.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <input
                      type="checkbox"
                      id={`amenity-${amenity.id}`}
                      checked={selectedAmenities.includes(amenity.id)}
                      onChange={() => handleAmenityToggle(amenity.id)}
                      className="h-5 w-5 text-green-600 rounded border-gray-300 focus:ring-green-500"
                    />
                    <label htmlFor={`amenity-${amenity.id}`} className="flex flex-col cursor-pointer">
                      <span className="font-medium text-gray-700">{amenity.name}</span>
                      <span className="text-xs text-gray-500">{amenity.description}</span>
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional fields */}
            <div className="col-span-1 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  อ้างอิง
                </label>
                <input
                  type="text"
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ที่มาของภาพอธิบาย
                </label>
                <input
                  type="text"
                  value={imageSource}
                  onChange={(e) => setImageSource(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                />
              </div>
            </div>

            <div className="col-span-1 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  คำอธิบายภาพประกอบบทความ
                </label>
                <input
                  type="text"
                  value={imageDescription}
                  onChange={(e) => setImageDescription(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Highlights
                </label>
                <div className="border border-gray-300 rounded-lg relative">
                  <textarea
                    className="w-full p-3 focus:outline-none text-sm h-32 rounded-lg focus:ring-2 focus:ring-green-500 transition-all"
                    placeholder="Add highlights here..."
                    value={highlights}
                    onChange={(e) => setHighlights(e.target.value)}
                  ></textarea>
                  <button className="absolute right-3 bottom-3 text-gray-400 hover:text-gray-600 transition-colors">
                    <ChevronDown size={18} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Submit button */}
        <div className="flex justify-end mt-6">
          <button
            className="bg-gray-300 text-gray-700 py-2 px-4 rounded-lg shadow hover:bg-gray-400 transition duration-200 mr-3"
            onClick={() => navigate(-1)}
          >
            Cancel
          </button>
          <button
            className="bg-green-600 text-white py-2 px-6 rounded-lg shadow-md hover:bg-green-700 transition duration-200"
            onClick={() => {
              // Validate required fields
              if (!businessName || !businessType || !shortDescription || !contactNumber || !social || !storyUrl) {
                Swal.fire({
                  title: 'Missing Information',
                  text: 'Please fill in all required fields marked with *',
                  icon: 'warning',
                  confirmButtonColor: '#10B981',
                });
                return;
              }

              // Show confirmation dialog
              Swal.fire({
                title: isEditMode ? 'Update Business Information?' : 'Submit New Business?',
                html: `
                  <div class="text-left mb-4">
                    <p class="font-medium text-gray-700">${businessName}</p>
                    <p class="text-sm text-gray-500">${businessType}</p>
                    <p class="text-sm text-gray-500">${shortDescription}</p>
                  </div>
                  <p class="mb-4">Are you sure you want to ${isEditMode ? 'update' : 'submit'} this business information?</p>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#10B981',
                cancelButtonColor: '#6B7280',
                confirmButtonText: isEditMode ? 'Update' : 'Submit',
                cancelButtonText: 'Cancel',
              }).then((result) => {
                if (result.isConfirmed) {
                  // Handle form submission
                  // In a real app, you would send the data to your backend here
                  console.log('Form submitted:', {
                    businessName,
                    businessType,
                    shortDescription,
                    contactNumber,
                    social,
                    description,
                    storyUrl,
                    reference,
                    imageSource,
                    imageDescription,
                    highlights,
                    selectedAmenities,
                    imageFile: imageFile ? imageFile.name : 'No image'
                  });

                  // Show success message
                  Swal.fire({
                    title: isEditMode ? 'Updated!' : 'Submitted!',
                    text: isEditMode
                      ? `${businessName} has been updated successfully.`
                      : `${businessName} has been submitted successfully.`,
                    icon: 'success',
                    confirmButtonColor: '#10B981',
                  }).then(() => {
                    // Navigate back to the stores page
                    navigate('/stores');
                  });
                }
              });
            }}
          >
            {isEditMode ? "Update" : "Submit"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContentManage;