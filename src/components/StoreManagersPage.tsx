import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUserRole } from "../contexts/UserRoleContext";
import {
  UserIcon,
  MagnifyingGlassIcon,
  BuildingStorefrontIcon,
  PlusIcon,
  TrashIcon,
  PencilSquareIcon,
  ArrowPathIcon,
  EnvelopeIcon,
  PhoneIcon,
} from "@heroicons/react/24/outline";
import { FiUsers } from "react-icons/fi";

const StoreManagersPage: React.FC = () => {
  const navigate = useNavigate();
  const { userRole } = useUserRole();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStore, setSelectedStore] = useState<string>("all");

  // For demo purposes, we'll simulate the current user's ID
  const currentUserId = "owner1";

  // Sample stores owned by the current Store Owner
  const ownedStores = [
    { id: "store1", name: "Seaside Tours" },
    { id: "store2", name: "Mountain Adventures" },
  ];

  // Sample store managers data
  const storeManagers = [
    {
      id: "manager1",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+66 89 123 4567",
      avatar: "JS",
      assignedStores: ["store1"],
      status: "active",
      lastActive: "2 hours ago",
      permissions: ["Edit Content", "View Analytics"],
    },
    {
      id: "manager2",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "+66 81 987 6543",
      avatar: "SJ",
      assignedStores: ["store1", "store2"],
      status: "active",
      lastActive: "1 day ago",
      permissions: ["Edit Content", "View Analytics", "Publish Content"],
    },
    {
      id: "manager3",
      name: "Michael Wong",
      email: "<EMAIL>",
      phone: "+66 82 456 7890",
      avatar: "MW",
      assignedStores: ["store2"],
      status: "inactive",
      lastActive: "1 week ago",
      permissions: ["Edit Content"],
    },
  ];

  // Redirect if not a Store Owner
  useEffect(() => {
    if (userRole !== "Store Owner") {
      navigate("/");
    }
  }, [userRole, navigate]);

  // Filter managers based on search query and selected store
  const filteredManagers = storeManagers.filter((manager) => {
    const matchesSearch = 
      manager.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      manager.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStore = 
      selectedStore === "all" || 
      manager.assignedStores.includes(selectedStore);
    
    return matchesSearch && matchesStore;
  });

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Store Managers</h1>
          <p className="text-sm text-gray-500">
            Manage the users who have access to your stores
          </p>
        </div>
        <button
          onClick={() => navigate("/add-store-manager")}
          className="flex items-center px-4 py-2 rounded-lg bg-green-600 text-white hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          <span>Add New Manager</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search by name or email"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div>
              <label htmlFor="store-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Store
              </label>
              <select
                id="store-filter"
                value={selectedStore}
                onChange={(e) => setSelectedStore(e.target.value)}
                className="block w-full border border-gray-300 rounded-lg p-2 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
              >
                <option value="all">All Stores</option>
                {ownedStores.map((store) => (
                  <option key={store.id} value={store.id}>
                    {store.name}
                  </option>
                ))}
              </select>
            </div>
            <button className="p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 text-gray-500 self-end">
              <ArrowPathIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
          <div className="bg-blue-100 p-4 rounded-lg text-blue-600">
            <UserIcon className="h-6 w-6" />
          </div>
          <div className="ml-4">
            <div className="text-3xl font-bold text-gray-800">{storeManagers.length}</div>
            <div className="text-sm text-gray-500">Total Managers</div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
          <div className="bg-green-100 p-4 rounded-lg text-green-600">
            <BuildingStorefrontIcon className="h-6 w-6" />
          </div>
          <div className="ml-4">
            <div className="text-3xl font-bold text-gray-800">{ownedStores.length}</div>
            <div className="text-sm text-gray-500">Managed Stores</div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
          <div className="bg-purple-100 p-4 rounded-lg text-purple-600">
            <FiUsers className="h-6 w-6" />
          </div>
          <div className="ml-4">
            <div className="text-3xl font-bold text-gray-800">
              {storeManagers.filter(manager => manager.status === "active").length}
            </div>
            <div className="text-sm text-gray-500">Active Managers</div>
          </div>
        </div>
      </div>

      {/* Managers Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-lg font-medium text-gray-800">Store Managers</h2>
        </div>
        
        {filteredManagers.length === 0 ? (
          <div className="p-6 text-center">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <UserIcon className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-800 mb-1">No managers found</h3>
            <p className="text-gray-500 mb-4">
              {searchQuery 
                ? "Try adjusting your search or filters" 
                : "You haven't added any store managers yet"}
            </p>
            <button
              onClick={() => navigate("/add-store-manager")}
              className="inline-flex items-center px-4 py-2 rounded-lg bg-green-600 text-white hover:bg-green-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              <span>Add New Manager</span>
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Manager
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned Stores
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Permissions
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredManagers.map((manager) => (
                  <tr key={manager.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-medium">
                          {manager.avatar}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{manager.name}</div>
                          <div className="text-sm text-gray-500">Last active {manager.lastActive}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <div className="flex items-center text-sm text-gray-500">
                          <EnvelopeIcon className="h-4 w-4 mr-1 text-gray-400" />
                          {manager.email}
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <PhoneIcon className="h-4 w-4 mr-1 text-gray-400" />
                          {manager.phone}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {manager.assignedStores.map((storeId) => {
                          const store = ownedStores.find(s => s.id === storeId);
                          return store ? (
                            <span 
                              key={storeId}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              <BuildingStorefrontIcon className="h-3 w-3 mr-1" />
                              {store.name}
                            </span>
                          ) : null;
                        })}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {manager.permissions.map((permission, index) => (
                          <span 
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            {permission}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {manager.status === "active" ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <span className="h-2 w-2 rounded-full bg-green-500 mr-1"></span>
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <span className="h-2 w-2 rounded-full bg-gray-500 mr-1"></span>
                          Inactive
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button 
                          className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-colors"
                          title="Edit Manager"
                        >
                          <PencilSquareIcon className="h-5 w-5" />
                        </button>
                        <button 
                          className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                          title="Remove Manager"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default StoreManagersPage;
