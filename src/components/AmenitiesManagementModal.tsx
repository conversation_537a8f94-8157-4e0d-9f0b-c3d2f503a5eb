import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';

// Create a SweetAlert instance with React content
const MySwal = withReactContent(Swal);

// Define the Amenity type
export interface Amenity {
  id: number;
  name: string;
  description: string;
  icon: string;
  createdAt: string;
}

interface AmenitiesManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (amenity: Amenity) => void;
  amenity?: Amenity | null;
  mode: 'add' | 'edit';
}

const AmenitiesManagementModal: React.FC<AmenitiesManagementModalProps> = ({
  isOpen,
  onClose,
  onSave,
  amenity,
  mode
}) => {
  const [formData, setFormData] = useState<Partial<Amenity>>({
    name: '',
    description: '',
    icon: ''
  });
  
  const [errors, setErrors] = useState({
    name: false,
    description: false,
    icon: false
  });

  // Update form data when amenity changes
  useEffect(() => {
    if (amenity && mode === 'edit') {
      setFormData({
        id: amenity.id,
        name: amenity.name,
        description: amenity.description,
        icon: amenity.icon,
        createdAt: amenity.createdAt
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: '',
        description: '',
        icon: ''
      });
    }
  }, [amenity, mode]);

  // Show the modal when isOpen changes to true
  useEffect(() => {
    if (isOpen) {
      showModal();
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors = {
      name: !formData.name,
      description: !formData.description,
      icon: !formData.icon
    };
    
    setErrors(newErrors);
    return !newErrors.name && !newErrors.description && !newErrors.icon;
  };

  const handleSave = () => {
    if (!validateForm()) {
      return;
    }
    
    // For add mode, generate a new ID
    if (mode === 'add' && !formData.id) {
      formData.id = Math.floor(Math.random() * 10000);
      formData.createdAt = new Date().toISOString().split('T')[0];
    }
    
    // Call the onSave callback with the form data
    onSave(formData as Amenity);
  };

  const showModal = () => {
    MySwal.fire({
      title: mode === 'add' ? 'เพิ่มสิ่งอำนวยความสะดวกใหม่' : 'แก้ไขสิ่งอำนวยความสะดวก',
      html: `
        <form id="amenityForm" class="text-left">
          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              ชื่อสิ่งอำนวยความสะดวก <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value="${formData.name || ''}"
              class="w-full border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อสิ่งอำนวยความสะดวก"
            />
            ${errors.name ? '<p class="mt-1 text-xs text-red-500">กรุณากรอกชื่อสิ่งอำนวยความสะดวก</p>' : ''}
          </div>
          
          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              คำอธิบาย <span class="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows="3"
              class="w-full border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="คำอธิบายสิ่งอำนวยความสะดวก"
            >${formData.description || ''}</textarea>
            ${errors.description ? '<p class="mt-1 text-xs text-red-500">กรุณากรอกคำอธิบาย</p>' : ''}
          </div>
          
          <div class="mb-4">
            <label for="icon" class="block text-sm font-medium text-gray-700 mb-1">
              ไอคอน <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="icon"
              value="${formData.icon || ''}"
              class="w-full border ${errors.icon ? 'border-red-500' : 'border-gray-300'} rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อไอคอน (เช่น wifi, car, coffee)"
            />
            ${errors.icon ? '<p class="mt-1 text-xs text-red-500">กรุณากรอกชื่อไอคอน</p>' : ''}
            <p class="mt-1 text-xs text-gray-500">ใส่ชื่อไอคอนที่ต้องการใช้ (เช่น wifi, car, coffee)</p>
          </div>
        </form>
      `,
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: mode === 'add' ? 'เพิ่ม' : 'บันทึก',
      cancelButtonText: 'ยกเลิก',
      didOpen: () => {
        // Add event listeners to form elements
        const nameInput = document.getElementById('name') as HTMLInputElement;
        const descriptionTextarea = document.getElementById('description') as HTMLTextAreaElement;
        const iconInput = document.getElementById('icon') as HTMLInputElement;
        
        if (nameInput) {
          nameInput.addEventListener('input', (e) => {
            setFormData({
              ...formData,
              name: (e.target as HTMLInputElement).value
            });
          });
        }
        
        if (descriptionTextarea) {
          descriptionTextarea.addEventListener('input', (e) => {
            setFormData({
              ...formData,
              description: (e.target as HTMLTextAreaElement).value
            });
          });
        }
        
        if (iconInput) {
          iconInput.addEventListener('input', (e) => {
            setFormData({
              ...formData,
              icon: (e.target as HTMLInputElement).value
            });
          });
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        handleSave();
      } else {
        onClose();
      }
    });
  };

  return null; // The modal is rendered by SweetAlert2
};

export default AmenitiesManagementModal;
