import React from "react";
import { useUserRole } from "../contexts/UserRoleContext";
import AdminDashboard from "./dashboards/AdminDashboard";
import StoreOwnerDashboard from "./dashboards/StoreOwnerDashboard";
import StoreManagerDashboard from "./dashboards/StoreManagerDashboard";

const RoleDashboard: React.FC = () => {
  const { userRole } = useUserRole();

  // Render the appropriate dashboard based on user role
  switch (userRole) {
    case "Admin":
      return <AdminDashboard />;
    case "Store Owner":
      return <StoreOwnerDashboard />;
    case "Store Manager":
      return <StoreManagerDashboard />;
    default:
      return <div>Unknown role</div>;
  }
};

export default RoleDashboard;
