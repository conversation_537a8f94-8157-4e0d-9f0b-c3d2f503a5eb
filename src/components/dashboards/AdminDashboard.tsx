import React from "react";
import { useNavigate } from "react-router-dom";
import {
  UsersIcon,
  BuildingStorefrontIcon,
  DocumentCheckIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  BellAlertIcon,
} from "@heroicons/react/24/outline";

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();

  // Sample data for the dashboard
  const stats = {
    totalUsers: 124,
    totalStores: 48,
    pendingApprovals: 12,
    activeStores: 42,
  };

  // Sample recent activities
  const recentActivities = [
    {
      id: 1,
      action: "สร้างร้านค้าใหม่",
      user: "สมชาย ใจดี",
      time: "2 ชั่วโมงที่แล้ว",
      status: "success",
    },
    {
      id: 2,
      action: "อัปเดตเนื้อหารออนุมัติ",
      user: "สุดา วงศ์ใหญ่",
      time: "4 ชั่วโมงที่แล้ว",
      status: "pending",
    },
    {
      id: 3,
      action: "ผู้ใช้ใหม่ลงทะเบียน",
      user: "วิชัย สมบูรณ์",
      time: "เมื่อวาน",
      status: "success",
    },
    {
      id: 4,
      action: "การอัปเดตร้านค้าถูกปฏิเสธ",
      user: "นิดา ศรีสุข",
      time: "เมื่อวาน",
      status: "rejected",
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800">แดชบอร์ดผู้ดูแลระบบ</h1>
        <p className="text-sm text-gray-500">
          ภาพรวมระบบทั้งหมดพร้อมสิทธิ์การเข้าถึงแบบผู้ดูแลระบบ
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <button
          onClick={() => navigate("/users")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-indigo-100 text-indigo-600 mr-4">
            <UsersIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">จัดการผู้ใช้</h3>
            <p className="text-sm text-gray-500">จัดการผู้ใช้ทั้งหมดในระบบ</p>
          </div>
        </button>

        <button
          onClick={() => navigate("/stores")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-green-100 text-green-600 mr-4">
            <BuildingStorefrontIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">จัดการร้านค้า</h3>
            <p className="text-sm text-gray-500">จัดการร้านค้าทั้งหมด</p>
          </div>
        </button>

        <button
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-amber-100 text-amber-600 mr-4">
            <DocumentCheckIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">อนุมัติเนื้อหา</h3>
            <p className="text-sm text-gray-500">{stats.pendingApprovals} รออนุมัติ</p>
          </div>
        </button>

        <button
          onClick={() => navigate("/roleconfig")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-purple-100 text-purple-600 mr-4">
            <Cog6ToothIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">ตั้งค่าระบบ</h3>
            <p className="text-sm text-gray-500">กำหนดค่าระบบ</p>
          </div>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">ผู้ใช้ทั้งหมด</h3>
            <UsersIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">{stats.totalUsers}</div>
          <div className="mt-2 text-sm text-green-600">+12% จากเดือนที่แล้ว</div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">ร้านค้าทั้งหมด</h3>
            <BuildingStorefrontIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">{stats.totalStores}</div>
          <div className="mt-2 text-sm text-green-600">+8% จากเดือนที่แล้ว</div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">ร้านค้าที่เปิดใช้งาน</h3>
            <BuildingStorefrontIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">{stats.activeStores}</div>
          <div className="mt-2 text-sm text-green-600">87.5% ของทั้งหมด</div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">สถานะระบบ</h3>
            <ChartBarIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-green-600">ดีเยี่ยม</div>
          <div className="mt-2 text-sm text-green-600">ระบบทำงานปกติทั้งหมด</div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-800">กิจกรรมล่าสุด</h2>
            <BellAlertIcon className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        <div className="divide-y divide-gray-100">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">{activity.action}</div>
                  <div className="text-sm text-gray-500">
                    โดย {activity.user} • {activity.time}
                  </div>
                </div>
                <div>
                  {activity.status === "success" && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      สำเร็จ
                    </span>
                  )}
                  {activity.status === "pending" && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                      รออนุมัติ
                    </span>
                  )}
                  {activity.status === "rejected" && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      ปฏิเสธ
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <button
            onClick={() => navigate("/activity-log")}
            className="w-full text-center text-sm text-gray-600 hover:text-gray-900 font-medium"
          >
            ดูกิจกรรมทั้งหมด
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
