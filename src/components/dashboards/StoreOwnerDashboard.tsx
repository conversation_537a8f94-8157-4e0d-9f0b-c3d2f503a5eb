import React from "react";
import { useNavigate } from "react-router-dom";
import {
  BuildingStorefrontIcon,
  UsersIcon,
  ChartBarIcon,
  StarIcon,
  EyeIcon,
} from "@heroicons/react/24/outline";

const StoreOwnerDashboard: React.FC = () => {
  const navigate = useNavigate();

  // Sample data for the dashboard
  const stats = {
    totalStores: 5,
    totalManagers: 8,
    totalViews: 1248,
    averageRating: 4.7,
  };

  // Sample store data
  const stores = [
    {
      id: "1998498",
      title: "Seaside Tours",
      location: "กรุงเทพมหานคร",
      status: "active",
      views: 423,
      rating: 4.8,
    },
    {
      id: "1998499",
      title: "Mountain Adventures",
      location: "เชียงใหม่",
      status: "active",
      views: 356,
      rating: 4.6,
    },
    {
      id: "1998500",
      title: "City Explorers",
      location: "นครนายก",
      status: "pending",
      views: 289,
      rating: 4.5,
    },
    {
      id: "1998501",
      title: "Desert Safaris",
      location: "นครนายก",
      status: "inactive",
      views: 180,
      rating: 4.2,
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800">
          Store Owner Dashboard
        </h1>
        <p className="text-sm text-gray-500">
          Manage your stores, content, and view performance metrics
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* <button
          onClick={() => navigate("/content/edit/new")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-green-100 text-green-600 mr-4">
            <PlusCircleIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">Add New Store</h3>
            <p className="text-sm text-gray-500">
              Create a new business listing
            </p>
          </div>
        </button> */}

        <button
          onClick={() => navigate("/stores")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-blue-100 text-blue-600 mr-4">
            <BuildingStorefrontIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">Manage Stores</h3>
            <p className="text-sm text-gray-500">Edit your existing stores</p>
          </div>
        </button>

        <button
          onClick={() => navigate("/store-managers")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-purple-100 text-purple-600 mr-4">
            <UsersIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">Manage Sub-accounts</h3>
            <p className="text-sm text-gray-500">View and manage store managers</p>
          </div>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">Your Stores</h3>
            <BuildingStorefrontIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {stats.totalStores}
          </div>
          <div className="mt-2 text-sm text-green-600">
            {stores.filter((store) => store.status === "active").length} active
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">
              Store Managers
            </h3>
            <UsersIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {stats.totalManagers}
          </div>
          <div className="mt-2 text-sm text-gray-500">Across all stores</div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">Total Views</h3>
            <EyeIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {stats.totalViews}
          </div>
          <div className="mt-2 text-sm text-green-600">
            +24% from last month
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">
              Average Rating
            </h3>
            <StarIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {stats.averageRating}
          </div>
          <div className="mt-2 text-sm text-green-600">
            +0.2 from last month
          </div>
        </div>
      </div>

      {/* Store Performance */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-800">
              Store Performance
            </h2>
            <ChartBarIcon className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Store Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Location
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Views
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Rating
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {stores.map((store) => (
                <tr
                  key={store.id}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">
                      {store.title}
                    </div>
                    <div className="text-sm text-gray-500">#{store.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {store.location}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {store.status === "active" && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <span className="h-2 w-2 rounded-full bg-green-500 mr-2" />
                        ใช้งาน
                      </span>
                    )}
                    {store.status === "pending" && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                        <span className="h-2 w-2 rounded-full bg-amber-500 mr-2" />
                        กำลังรอ
                      </span>
                    )}
                    {store.status === "inactive" && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <span className="h-2 w-2 rounded-full bg-red-500 mr-2" />
                        พัก/ยกเลิก
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {store.views}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-700 mr-2">
                        {store.rating}
                      </span>
                      <div className="flex text-yellow-400">
                        <StarIcon className="h-4 w-4 fill-current" />
                        <StarIcon className="h-4 w-4 fill-current" />
                        <StarIcon className="h-4 w-4 fill-current" />
                        <StarIcon className="h-4 w-4 fill-current" />
                        <StarIcon className="h-4 w-4 fill-current text-gray-300" />
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => navigate(`/content/edit/${store.id}`)}
                      className="text-green-600 hover:text-green-900 mr-3"
                    >
                      Edit
                    </button>
                    <button className="text-blue-600 hover:text-blue-900">
                      Analytics
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default StoreOwnerDashboard;
