import React from "react";
import { useNavigate } from "react-router-dom";
import {
  DocumentTextIcon,
  PencilSquareIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";

const StoreManagerDashboard: React.FC = () => {
  const navigate = useNavigate();

  // Sample data for the dashboard
  const stats = {
    totalContent: 24,
    pendingApproval: 3,
    approved: 18,
    rejected: 3,
    totalViews: 856,
    totalComments: 42,
  };

  // Sample content data
  const contentItems = [
    {
      id: "C1001",
      title: "Seaside Tours Summer Special",
      type: "Promotion",
      store: "Seaside Tours",
      status: "approved",
      lastUpdated: "2 days ago",
      views: 245,
    },
    {
      id: "C1002",
      title: "New Boat Tour Experience",
      type: "Article",
      store: "Seaside Tours",
      status: "pending",
      lastUpdated: "1 day ago",
      views: 0,
    },
    {
      id: "C1003",
      title: "Customer Testimonials",
      type: "Article",
      store: "Seaside Tours",
      status: "approved",
      lastUpdated: "1 week ago",
      views: 189,
    },
    {
      id: "C1004",
      title: "Winter Discount Package",
      type: "Promotion",
      store: "Seaside Tours",
      status: "rejected",
      lastUpdated: "3 days ago",
      views: 0,
    },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800">
          Store Manager Dashboard
        </h1>
        <p className="text-sm text-gray-500">
          Manage content and view performance for your assigned stores
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <button
          onClick={() => navigate("/content/edit/new")}
          className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors"
        >
          <div className="p-3 rounded-lg bg-green-100 text-green-600 mr-4">
            <DocumentTextIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">Create New Content</h3>
            <p className="text-sm text-gray-500">
              Add a new article or promotion
            </p>
          </div>
        </button>

        <button className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors">
          <div className="p-3 rounded-lg bg-blue-100 text-blue-600 mr-4">
            <PencilSquareIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">Edit Draft Content</h3>
            <p className="text-sm text-gray-500">Continue working on drafts</p>
          </div>
        </button>

        <button className="flex items-center p-4 bg-white rounded-xl shadow-sm border border-gray-100 hover:bg-gray-50 transition-colors">
          <div className="p-3 rounded-lg bg-purple-100 text-purple-600 mr-4">
            <ChartBarIcon className="h-6 w-6" />
          </div>
          <div className="text-left">
            <h3 className="font-medium">View Analytics</h3>
            <p className="text-sm text-gray-500">See content performance</p>
          </div>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">
              Content Status
            </h3>
            <DocumentTextIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="flex items-center">
                <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                <span className="text-xs text-gray-500">Approved</span>
              </div>
              <div className="text-xl font-bold text-gray-800 mt-1">
                {stats.approved}
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center">
                <div className="h-3 w-3 rounded-full bg-amber-500 mr-2"></div>
                <span className="text-xs text-gray-500">Pending</span>
              </div>
              <div className="text-xl font-bold text-gray-800 mt-1">
                {stats.pendingApproval}
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center">
                <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                <span className="text-xs text-gray-500">Rejected</span>
              </div>
              <div className="text-xl font-bold text-gray-800 mt-1">
                {stats.rejected}
              </div>
            </div>
          </div>
        </div> */}

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">Total Views</h3>
            <ChartBarIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {stats.totalViews}
          </div>
          <div className="mt-2 text-sm text-green-600">
            +18% from last month
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-gray-500 text-sm font-medium">Comments</h3>
            <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="text-3xl font-bold text-gray-800">
            {stats.totalComments}
          </div>
          <div className="mt-2 text-sm text-green-600">12 new this week</div>
        </div>
      </div>

      {/* Content List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-800">Your Content</h2>
            <DocumentTextIcon className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Content
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Store
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Views
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {contentItems.map((item) => (
                <tr
                  key={item.id}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">
                      {item.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      Last updated {item.lastUpdated}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.store}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {item.status === "approved" && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                        Approved
                      </span>
                    )}
                    {item.status === "pending" && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        Pending
                      </span>
                    )}
                    {item.status === "rejected" && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <XCircleIcon className="h-4 w-4 mr-1" />
                        Rejected
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.views}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => navigate(`/content/edit/${item.id}`)}
                      className="text-green-600 hover:text-green-900 mr-3"
                    >
                      Edit
                    </button>
                    <button className="text-blue-600 hover:text-blue-900">
                      Preview
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default StoreManagerDashboard;
