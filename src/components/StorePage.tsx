import React, { useState } from "react";
import {
  FiE<PERSON>,
  FiTrash2,
  FiBarChart2,
  <PERSON><PERSON><PERSON>s,
  <PERSON>Eye,
  FiAlertCircle,
} from "react-icons/fi";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
import { useUserRole } from "../contexts/UserRoleContext";
import {
  MagnifyingGlassIcon,
  BuildingOfficeIcon,
  ClockIcon,
  XCircleIcon,
  AdjustmentsHorizontalIcon,
  CalendarIcon,
  ChevronDownIcon,
  ArrowPathIcon,
} from "@heroicons/react/24/outline";

// Define interfaces for type safety
interface RatingScore {
  title: string;
  score: number;
}

interface Store {
  id: string;
  title: string;
  date: string;
  createdBy: string;
  status: "active" | "inactive" | "pending";
  location: string;
  ownerId: string;
  managerIds: string[];
  ratingScores: RatingScore[];
  businessType?: string;
  shortDescription?: string;
  contactNumber?: string;
  social?: string;
  description?: string;
  storyUrl?: string;
  reference?: string;
  imageSource?: string;
  imageDescription?: string;
  highlights?: string;
}

const StorePage = () => {
  const navigate = useNavigate();

  // Get user role from context
  const { userRole } = useUserRole();
  // Sample store data (replace this with data fetched from your database)
  const [allStores, setAllStores] = useState<Store[]>([
    {
      id: "1998498",
      title: "Seaside Tours",
      date: "12-Jan-2024 | 08:53 am",
      createdBy: "John <PERSON>",
      status: "active",
      location: "กรุงเทพมหานคร",
      ownerId: "owner1", // ID of the store owner
      managerIds: ["manager1", "manager2"], // IDs of store managers who can access this store
      ratingScores: [
        { title: "เส้นทางวัตถุดิบ", score:   3.9 },
        { title: "การรับรู้ด้วยประสาทสัมผัส", score: 3.7 },
        { title: "ประสบการณ์ด้วยสร้างสรรค์อาหาร", score: 5.0 },
        { title: "วัฒนธรรมสัมพันธ์ด้านอาหาร", score: 4.5 },
      ],
    },
    {
      id: "1998499",
      title: "Mountain Adventures",
      date: "12-Jan-2024 | 08:53 am",
      createdBy: "Emily Johnson",
      status: "inactive",
      location: "เชียงใหม่",
      ownerId: "owner1", // Same owner as Seaside Tours
      managerIds: ["manager3"], // Different manager
      ratingScores: [
        { title: "เส้นทางวัตถุดิบ", score: 4.2 },
        { title: "การรับรู้ด้วยประสาทสัมผัส", score: 4.0 },
        { title: "ประสบการณ์ด้วยสร้างสรรค์อาหาร", score: 4.8 },
        { title: "วัฒนธรรมสัมพันธ์ด้านอาหาร", score: 4.3 },
      ],
    },
    {
      id: "1998500",
      title: "City Explorers",
      date: "13-Jan-2024 | 09:15 am",
      createdBy: "-------------",
      status: "pending",
      location: "นครนายก",
      ownerId: "owner2", // Different owner
      managerIds: ["manager1"], // Manager has access to multiple stores
      ratingScores: [
        { title: "เส้นทางวัตถุดิบ", score: 3.9 },
        { title: "การรับรู้ด้วยประสาทสัมผัส", score: 3.7 },
        { title: "ประสบการณ์ด้วยสร้างสรรค์อาหาร", score: 5.0 },
        { title: "วัฒนธรรมสัมพันธ์ด้านอาหาร", score: 4.5 },
      ],
    },
    {
      id: "1998501",
      title: "Desert Safaris",
      date: "14-Jan-2024 | 10:22 am",
      createdBy: "Sarah Chen",
      status: "active",
      location: "นครนายก",
      ownerId: "owner2",
      managerIds: ["manager4"],
      ratingScores: [
        { title: "เส้นทางวัตถุดิบ", score: 4.1 },
        { title: "การรับรู้ด้วยประสาทสัมผัส", score: 4.3 },
        { title: "ประสบการณ์ด้วยสร้างสรรค์อาหาร", score: 4.7 },
        { title: "วัฒนธรรมสัมพันธ์ด้านอาหาร", score: 4.2 },
      ],
    },
  ]);

  // For demo purposes, we'll simulate the current user's ID
  const currentUserId =
    userRole === "Store Owner"
      ? "owner1"
      : userRole === "Store Manager"
      ? "manager1"
      : "admin";

  // Filter stores based on user role and ownership
  const stores = allStores.filter((store) => {
    if (userRole === "Admin") {
      // Admins can see all stores
      return true;
    } else if (userRole === "Store Owner") {
      // Store owners can only see their own stores
      return store.ownerId === currentUserId;
    } else if (userRole === "Store Manager") {
      // Store managers can only see stores they're assigned to
      return store.managerIds.includes(currentUserId);
    }
    return false;
  });

  const [startDate, setStartDate] = useState("01-Jan-2024");
  const [endDate, setEndDate] = useState("30-Jan-2024");
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter stores based on the search term
  const filteredStores = stores.filter((store) =>
    store.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredStores.length / itemsPerPage);
  const paginatedStores = filteredStores.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="flex h-screen bg-gray-50 text-gray-800">
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto p-6">
          {/* Page Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                {userRole === "Admin"
                  ? "All Stores"
                  : userRole === "Store Owner"
                  ? "Your Stores"
                  : "Assigned Stores"}
              </h1>
              <p className="text-sm text-gray-500">
                {userRole === "Admin" &&
                  "Complete access to all stores and management features"}
                {userRole === "Store Owner" &&
                  `Showing ${stores.length} stores owned by you`}
                {userRole === "Store Manager" &&
                  `Showing ${stores.length} stores you are assigned to manage`}
              </p>
              {stores.length === 0 && (
                <p className="mt-2 text-sm text-red-500 font-medium">
                  {userRole === "Store Owner"
                    ? "You don't own any stores yet. Contact an admin to add stores to your account."
                    : userRole === "Store Manager"
                    ? "You haven't been assigned to any stores yet. Contact a store owner for access."
                    : ""}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {/* Role-specific action button */}
              {userRole === "Admin" && (
                <button className="flex items-center px-4 py-2 rounded-lg bg-green-600 text-white hover:bg-green-700">
                  <FiUsers className="h-5 w-5 mr-2" />
                  <span>Manage Users</span>
                </button>
              )}

              {userRole === "Store Manager" && (
                <button className="flex items-center px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                  <FiBarChart2 className="h-5 w-5 mr-2" />
                  <span>View Reports</span>
                </button>
              )}

              <button className="p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 text-gray-500">
                <ArrowPathIcon className="h-5 w-5" />
              </button>
              <button
                className="flex items-center px-4 py-2 rounded-lg bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
                onClick={() => setShowFilters(!showFilters)}
              >
                <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
                <span>Filters</span>
                <ChevronDownIcon
                  className={`h-4 w-4 ml-2 transition-transform ${
                    showFilters ? "rotate-180" : ""
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
              <div className="bg-green-100 p-4 rounded-lg text-green-600">
                <BuildingOfficeIcon className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <div className="text-3xl font-bold text-gray-800">
                  {stores.length}
                </div>
                <div className="text-sm text-gray-500">
                  {userRole === "Admin"
                    ? "ที่ทั้งหมด"
                    : userRole === "Store Owner"
                    ? "ธุรกิจของคุณ"
                    : "ธุรกิจที่ดูแล"}
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
              <div className="bg-amber-100 p-4 rounded-lg text-amber-600">
                <ClockIcon className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <div className="text-3xl font-bold text-gray-800">
                  {stores.filter((store) => store.status === "pending").length}
                </div>
                <div className="text-sm text-gray-500">กำลังรอ</div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
              <div className="bg-red-100 p-4 rounded-lg text-red-600">
                <XCircleIcon className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <div className="text-3xl font-bold text-gray-800">
                  {stores.filter((store) => store.status === "inactive").length}
                </div>
                <div className="text-sm text-gray-500">พัก/ยกเลิก</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            {/* Filter Section */}
            {showFilters && (
              <div className="p-6 border-b border-gray-100">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="col-span-1">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      วันที่เริ่มต้น
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                      />
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                  <div className="col-span-1">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      วันที่สิ้นสุด
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                      />
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                  <div className="col-span-1">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ประเภทสถานที่
                    </label>
                    <div className="relative">
                      <select className="w-full border border-gray-300 rounded-lg p-2 pr-10 text-sm appearance-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all">
                        <option>ทั้งหมด</option>
                        <option>ร้านอาหาร</option>
                        <option>คาเฟ่</option>
                        <option>โรงแรม</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                  <div className="col-span-1 flex items-end">
                    <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg w-full text-sm font-medium transition-colors flex items-center justify-center">
                      <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                      ค้นหาข้อมูล
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Search and Table */}
            <div className="p-6">
              <div className="relative mb-6">
                <input
                  type="text"
                  placeholder="ค้นหาด้วยชื่อหรือไอดี"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                />
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
              </div>

              {/* Table */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        ไอดี
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        ชื่อธุรกิจ
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        วันที่บันทึกเข้า
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        ยืนยันโดย(แอดมิน)
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        สถานะ
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        จัดการ
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedStores.map((store) => (
                      <tr
                        key={store.id}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          #{store.id}
                          {userRole === "Admin" && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                              {store.ownerId === "owner1"
                                ? "Owner 1"
                                : "Owner 2"}
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                          {store.title}
                          {userRole === "Store Manager" &&
                            store.ownerId === "owner1" && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Owner: John Smith
                              </span>
                            )}
                          {userRole === "Store Manager" &&
                            store.ownerId === "owner2" && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Owner: Emily Johnson
                              </span>
                            )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {store.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {store.createdBy}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {store.status === "active" && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <span className="h-2 w-2 rounded-full bg-green-500 mr-2" />
                              ใช้งาน
                            </span>
                          )}
                          {store.status === "pending" && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                              <span className="h-2 w-2 rounded-full bg-amber-500 mr-2" />
                              กำลังรอ
                            </span>
                          )}
                          {store.status === "inactive" && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <span className="h-2 w-2 rounded-full bg-red-500 mr-2" />
                              พัก/ยกเลิก
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                          <div className="flex justify-end space-x-2">
                            {/* Edit button - available for all roles */}
                            <button
                              className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50 transition-colors"
                              title="Edit"
                              onClick={() => {
                                // Create a more complete store data object with additional fields
                                const storeData = {
                                  ...store,
                                  businessType: store.location, // Using location as businessType for demo
                                  shortDescription: `Description for ${store.title}`,
                                  contactNumber: "099-999-9999", // Demo data
                                  social: "@" + store.title.replace(/ /g, ""),
                                  description: `Detailed description for ${store.title} located in ${store.location}.`,
                                  storyUrl: "https://example.com/" + store.id,
                                  reference: "",
                                  imageSource: "",
                                  imageDescription: "",
                                  highlights: `Key highlights for ${store.title}`,
                                };

                                // Navigate to the content management page with the store ID and data
                                navigate(`/content/edit/${store.id}`, {
                                  state: { storeData },
                                });
                              }}
                            >
                              <FiEdit className="w-5 h-5" />
                            </button>

                            {/* Admin-specific buttons */}
                            {userRole === "Admin" && (
                              <>
                                <button
                                  className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                                  title="Delete"
                                >
                                  <FiTrash2 className="w-5 h-5" />
                                </button>
                                {store.status === "pending" && (
                                  <button
                                    className="text-amber-600 hover:text-amber-900 p-1 rounded-full hover:bg-amber-50 transition-colors"
                                    title="Review"
                                    onClick={() => {
                                      Swal.fire({
                                        title: 'Review Store',
                                        html: `
                                          <div class="text-left mb-4">
                                            <p class="font-medium text-gray-700">Store: ${store.title}</p>
                                            <p class="text-sm text-gray-500">ID: #${store.id}</p>
                                            <p class="text-sm text-gray-500">Created by: ${store.createdBy}</p>
                                            <p class="text-sm text-gray-500">Location: ${store.location}</p>
                                          </div>
                                          <div class="text-left mb-4">
                                            <p class="font-medium text-gray-700 mb-2">คะแนนการประเมิน:</p>
                                            ${store.ratingScores.map(score => `
                                              <div class="flex justify-between items-center mb-1">
                                                <span class="text-sm text-gray-600">${score.title}</span>
                                                <div class="flex items-center">
                                                  <span class="text-sm font-medium text-gray-700 mr-2">${score.score.toFixed(1)}</span>
                                                  <div class="w-24 bg-gray-200 rounded-full h-2.5">
                                                    <div class="bg-green-600 h-2.5 rounded-full" style="width: ${(score.score / 5) * 100}%"></div>
                                                  </div>
                                                </div>
                                              </div>
                                            `).join('')}
                                            <div class="flex justify-between items-center mt-3 pt-2 border-t border-gray-200">
                                              <span class="text-sm font-medium text-gray-700">คะแนนเฉลี่ย</span>
                                              <span class="text-sm font-medium text-gray-700">
                                                ${(store.ratingScores.reduce((sum, item) => sum + item.score, 0) / store.ratingScores.length).toFixed(1)}
                                              </span>
                                            </div>
                                          </div>
                                          <p class="mb-4 mt-4">Do you want to approve or reject this store?</p>
                                        `,
                                        icon: 'question',
                                        showCancelButton: true,
                                        showDenyButton: true,
                                        confirmButtonColor: '#10B981',
                                        denyButtonColor: '#EF4444',
                                        cancelButtonColor: '#6B7280',
                                        confirmButtonText: 'Approve',
                                        denyButtonText: 'Reject',
                                        cancelButtonText: 'Cancel',
                                      }).then((result: { isConfirmed: boolean; isDenied: boolean }) => {
                                        if (result.isConfirmed) {
                                          // Handle approve action
                                          // Update the store status to active
                                          const updatedStores = allStores.map(s =>
                                            s.id === store.id ? {...s, status: "active" as const} : s
                                          );
                                          setAllStores(updatedStores);
                                          console.log('Store approved:', store.id);
                                          Swal.fire('Approved!', `${store.title} has been approved.`, 'success');
                                        } else if (result.isDenied) {
                                          // Handle reject action
                                          const updatedStores = allStores.map(s =>
                                            s.id === store.id ? {...s, status: "inactive" as const} : s
                                          );
                                          setAllStores(updatedStores);
                                          console.log('Store rejected:', store.id);
                                          Swal.fire('Rejected!', `${store.title} has been rejected.`, 'error');
                                        }
                                      });
                                    }}
                                  >
                                    <FiAlertCircle className="w-5 h-5" />
                                  </button>
                                )}
                              </>
                            )}

                            {/* Store Owner-specific buttons */}
                            {userRole === "Store Owner" &&
                              store.ownerId === currentUserId && (
                                <>
                                  <button
                                    className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-colors"
                                    title="View Analytics"
                                  >
                                    <FiBarChart2 className="w-5 h-5" />
                                  </button>
                                  <button
                                    className="text-purple-600 hover:text-purple-900 p-1 rounded-full hover:bg-purple-50 transition-colors"
                                    title="Manage Sub-accounts"
                                  >
                                    <FiUsers className="w-5 h-5" />
                                  </button>
                                </>
                              )}

                            {/* Store Manager-specific buttons */}
                            {userRole === "Store Manager" &&
                              store.managerIds.includes(currentUserId) && (
                                <button
                                  className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-colors"
                                  title="View Details"
                                >
                                  <FiEye className="w-5 h-5" />
                                </button>
                              )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-500">
                  Showing{" "}
                  <span className="font-medium">
                    {(currentPage - 1) * itemsPerPage + 1}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium">
                    {Math.min(
                      currentPage * itemsPerPage,
                      filteredStores.length
                    )}
                  </span>{" "}
                  of{" "}
                  <span className="font-medium">{filteredStores.length}</span>{" "}
                  results
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                    disabled={currentPage === 1}
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                  >
                    Previous
                  </button>
                  {Array.from({ length: totalPages }, (_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentPage(index + 1)}
                      className={`px-3 py-1 border rounded-md text-sm font-medium ${
                        currentPage === index + 1
                          ? "bg-green-50 border-green-500 text-green-600"
                          : "border-gray-300 text-gray-700 hover:bg-gray-50"
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                  <button
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                    disabled={currentPage === totalPages}
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StorePage;
