import React, { useState } from "react";
import {
  MagnifyingGlassIcon,
  CalendarIcon,
  ChevronDownIcon,
  ArrowPathIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { useUserRole } from "../contexts/UserRoleContext";
import { useNavigate } from "react-router-dom";

// Define activity log entry interface
interface ActivityLogEntry {
  id: number;
  action: string;
  actionType: "user" | "store" | "content" | "system" | "security";
  description: string;
  user: string;
  userId: string;
  userRole: string;
  ipAddress: string;
  timestamp: string;
  date: string;
  time: string;
  status: "success" | "pending" | "rejected" | "warning" | "error";
  details?: string;
}

const ActivityLogPage: React.FC = () => {
  const navigate = useNavigate();
  const { userRole } = useUserRole();
  
  // Redirect if not an Admin
  React.useEffect(() => {
    if (userRole !== "Admin") {
      navigate("/");
    }
  }, [userRole, navigate]);

  // State for filters
  const [startDate, setStartDate] = useState("2024-01-01");
  const [endDate, setEndDate] = useState("2024-12-31");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedActionType, setSelectedActionType] = useState("all");
  const [showFilters, setShowFilters] = useState(true);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Sample activity log data
  const activityLogs: ActivityLogEntry[] = [
    {
      id: 1,
      action: "User Login",
      actionType: "security",
      description: "User logged in successfully",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-15T08:30:45",
      date: "15-May-2024",
      time: "08:30 am",
      status: "success",
    },
    {
      id: 2,
      action: "Store Created",
      actionType: "store",
      description: "New store was created",
      user: "John Smith",
      userId: "user123",
      userRole: "Store Owner",
      ipAddress: "***********",
      timestamp: "2024-05-15T09:15:22",
      date: "15-May-2024",
      time: "09:15 am",
      status: "success",
      details: "Store ID: 1998500, Name: City Explorers",
    },
    {
      id: 3,
      action: "Content Update",
      actionType: "content",
      description: "Content was updated and pending approval",
      user: "Sarah Chen",
      userId: "user456",
      userRole: "Store Manager",
      ipAddress: "***********",
      timestamp: "2024-05-15T10:45:12",
      date: "15-May-2024",
      time: "10:45 am",
      status: "pending",
      details: "Content ID: C1002, Store: Seaside Tours",
    },
    {
      id: 4,
      action: "User Created",
      actionType: "user",
      description: "New user account was created",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-14T14:22:33",
      date: "14-May-2024",
      time: "02:22 pm",
      status: "success",
      details: "New Store Manager account for Robert Williams",
    },
    {
      id: 5,
      action: "Store Approval",
      actionType: "store",
      description: "Store was approved",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-14T15:10:05",
      date: "14-May-2024",
      time: "03:10 pm",
      status: "success",
      details: "Store ID: 1998499, Name: Mountain Adventures",
    },
    {
      id: 6,
      action: "Content Rejected",
      actionType: "content",
      description: "Content update was rejected",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-14T16:05:45",
      date: "14-May-2024",
      time: "04:05 pm",
      status: "rejected",
      details: "Content ID: C1004, Store: Seaside Tours, Reason: Inappropriate content",
    },
    {
      id: 7,
      action: "System Update",
      actionType: "system",
      description: "System settings were updated",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-13T11:30:22",
      date: "13-May-2024",
      time: "11:30 am",
      status: "success",
      details: "Updated role permissions for Store Manager role",
    },
    {
      id: 8,
      action: "Failed Login Attempt",
      actionType: "security",
      description: "Multiple failed login attempts detected",
      user: "Unknown",
      userId: "unknown",
      userRole: "Unknown",
      ipAddress: "************",
      timestamp: "2024-05-13T22:15:30",
      date: "13-May-2024",
      time: "10:15 pm",
      status: "warning",
      details: "5 failed attempts for username: john.smith",
    },
    {
      id: 9,
      action: "Store Deleted",
      actionType: "store",
      description: "Store was permanently deleted",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-12T09:45:12",
      date: "12-May-2024",
      time: "09:45 am",
      status: "success",
      details: "Store ID: 1998502, Name: Forest Retreat",
    },
    {
      id: 10,
      action: "Database Backup",
      actionType: "system",
      description: "Automated database backup completed",
      user: "System",
      userId: "system",
      userRole: "System",
      ipAddress: "localhost",
      timestamp: "2024-05-12T03:00:00",
      date: "12-May-2024",
      time: "03:00 am",
      status: "success",
    },
    {
      id: 11,
      action: "Permission Changed",
      actionType: "user",
      description: "User permissions were modified",
      user: "Admin User",
      userId: "admin1",
      userRole: "Admin",
      ipAddress: "***********",
      timestamp: "2024-05-11T14:22:33",
      date: "11-May-2024",
      time: "02:22 pm",
      status: "success",
      details: "Updated permissions for user: <EMAIL>",
    },
    {
      id: 12,
      action: "System Error",
      actionType: "system",
      description: "System encountered an error during operation",
      user: "System",
      userId: "system",
      userRole: "System",
      ipAddress: "localhost",
      timestamp: "2024-05-11T18:42:11",
      date: "11-May-2024",
      time: "06:42 pm",
      status: "error",
      details: "Error processing image upload: File size exceeded limit",
    },
  ];

  // Filter logs based on search query, date range, and action type
  const filteredLogs = activityLogs.filter((log) => {
    const matchesSearch = 
      log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (log.details && log.details.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const logDate = new Date(log.timestamp);
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    endDateObj.setHours(23, 59, 59, 999); // Set to end of day
    
    const inDateRange = logDate >= startDateObj && logDate <= endDateObj;
    
    const matchesActionType = selectedActionType === "all" || log.actionType === selectedActionType;
    
    return matchesSearch && inDateRange && matchesActionType;
  });

  // Sort logs by timestamp (newest first)
  const sortedLogs = [...filteredLogs].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Pagination logic
  const totalPages = Math.ceil(sortedLogs.length / itemsPerPage);
  const paginatedLogs = sortedLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Get status badge based on status
  const getStatusBadge = (status: ActivityLogEntry["status"]) => {
    switch (status) {
      case "success":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Success
          </span>
        );
      case "pending":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
            Pending
          </span>
        );
      case "rejected":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Rejected
          </span>
        );
      case "warning":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            Warning
          </span>
        );
      case "error":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Error
          </span>
        );
      default:
        return null;
    }
  };

  // Get action type badge
  const getActionTypeBadge = (actionType: ActivityLogEntry["actionType"]) => {
    switch (actionType) {
      case "user":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
            User
          </span>
        );
      case "store":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
            Store
          </span>
        );
      case "content":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
            Content
          </span>
        );
      case "system":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
            System
          </span>
        );
      case "security":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
            Security
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Activity Log</h1>
        <p className="text-sm text-gray-500">
          View and monitor all system activities and user actions
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
        {/* Filter Section */}
        {showFilters && (
          <div className="p-6 border-b border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  วันที่เริ่มต้น
                </label>
                <div className="relative">
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>
              <div className="col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  วันที่สิ้นสุด
                </label>
                <div className="relative">
                  <input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>
              <div className="col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ประเภทกิจกรรม
                </label>
                <div className="relative">
                  <select
                    value={selectedActionType}
                    onChange={(e) => setSelectedActionType(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg p-2 pr-10 text-sm appearance-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
                  >
                    <option value="all">ทั้งหมด</option>
                    <option value="user">ผู้ใช้</option>
                    <option value="store">ธุรกิจ</option>
                    <option value="content">เนื้อหา</option>
                    <option value="system">ระบบ</option>
                    <option value="security">ความปลอดภัย</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>
              <div className="col-span-1 flex items-end">
                <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg w-full text-sm font-medium transition-colors flex items-center justify-center">
                  <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                  ค้นหาข้อมูล
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Search and Table */}
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="relative flex-grow max-w-md">
              <input
                type="text"
                placeholder="ค้นหากิจกรรม, ผู้ใช้, หรือรายละเอียด"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 text-gray-500">
                <ArrowPathIcon className="h-5 w-5" />
              </button>
              <button
                className="flex items-center px-4 py-2 rounded-lg bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
                onClick={() => setShowFilters(!showFilters)}
              >
                <FunnelIcon className="h-5 w-5 mr-2" />
                <span>Filters</span>
                <ChevronDownIcon
                  className={`h-4 w-4 ml-2 transition-transform ${
                    showFilters ? "rotate-180" : ""
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Activity Log Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    กิจกรรม
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    ผู้ใช้
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    วันที่และเวลา
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    สถานะ
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedLogs.map((log) => (
                  <tr
                    key={log.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          <span className="font-medium text-gray-900 mr-2">
                            {log.action}
                          </span>
                          {getActionTypeBadge(log.actionType)}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          {log.description}
                        </div>
                        {log.details && (
                          <div className="text-xs text-gray-500 mt-1 italic">
                            {log.details}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {log.user}
                      </div>
                      <div className="text-xs text-gray-500">
                        {log.userRole} • {log.ipAddress}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{log.date}</div>
                      <div className="text-xs text-gray-500">{log.time}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(log.status)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-500">
              Showing{" "}
              <span className="font-medium">
                {(currentPage - 1) * itemsPerPage + 1}
              </span>{" "}
              to{" "}
              <span className="font-medium">
                {Math.min(currentPage * itemsPerPage, filteredLogs.length)}
              </span>{" "}
              of{" "}
              <span className="font-medium">{filteredLogs.length}</span>{" "}
              results
            </div>
            <div className="flex items-center space-x-2">
              <button
                className="p-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              {Array.from({ length: totalPages }, (_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index + 1)}
                  className={`px-3 py-1 border rounded-md text-sm font-medium ${
                    currentPage === index + 1
                      ? "bg-green-50 border-green-500 text-green-600"
                      : "border-gray-300 text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {index + 1}
                </button>
              ))}
              <button
                className="p-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                disabled={currentPage === totalPages}
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
              >
                <ChevronRightIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityLogPage;
