import React, { useState, useEffect } from "react";
import { FiEdit, FiTrash2, FiPlus, FiTag, FiPackage, FiClock } from "react-icons/fi";
import Swal from "sweetalert2";

// Define the Amenity type
export interface Amenity {
  id: number;
  name: string;
  description: string;
  icon: string;
  createdAt: string;
}

const AmenitiesManagement = () => {
  // Sample amenities data
  const [amenities, setAmenities] = useState([
    {
      id: 1,
      name: "Wi-Fi",
      description: "บริการอินเทอร์เน็ตไร้สาย",
      icon: "wifi",
      createdAt: "2023-05-15",
    },
    {
      id: 2,
      name: "ที่จอดรถ",
      description: "บริการที่จอดรถฟรี",
      icon: "car",
      createdAt: "2023-05-16",
    },
    {
      id: 3,
      name: "อาหารเช้า",
      description: "บริการอาหารเช้า",
      icon: "coffee",
      createdAt: "2023-05-17",
    },
    {
      id: 4,
      name: "สระว่ายน้ำ",
      description: "สระว่ายน้ำส่วนกลาง",
      icon: "water",
      createdAt: "2023-05-18",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [sortField, setSortField] = useState<keyof Amenity>("id");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Simulate loading data from API
  useEffect(() => {
    setLoading(true);
    // Simulate API call delay
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Sort and filter amenities
  const sortedAndFilteredAmenities = [...amenities]
    .filter(
      (amenity) =>
        amenity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        amenity.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortField === "id") {
        // Numeric sort
        return sortDirection === "asc"
          ? a[sortField] - b[sortField]
          : b[sortField] - a[sortField];
      } else {
        // String sort
        return sortDirection === "asc"
          ? String(a[sortField]).localeCompare(String(b[sortField]))
          : String(b[sortField]).localeCompare(String(a[sortField]));
      }
    });

  const handleOpenAddModal = () => {
    // Use SweetAlert2 directly for the modal
    Swal.fire({
      title: 'เพิ่มสิ่งอำนวยความสะดวกใหม่',
      html: `
        <form id="amenityForm" class="text-left">
          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              ชื่อสิ่งอำนวยความสะดวก <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อสิ่งอำนวยความสะดวก"
            />
          </div>

          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              คำอธิบาย <span class="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows="3"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="คำอธิบายสิ่งอำนวยความสะดวก"
            ></textarea>
          </div>

          <div class="mb-4">
            <label for="icon" class="block text-sm font-medium text-gray-700 mb-1">
              ไอคอน <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="icon"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อไอคอน (เช่น wifi, car, coffee)"
            />
            <p class="mt-1 text-xs text-gray-500">ใส่ชื่อไอคอนที่ต้องการใช้ (เช่น wifi, car, coffee)</p>
          </div>
        </form>
      `,
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'เพิ่ม',
      cancelButtonText: 'ยกเลิก',
      preConfirm: () => {
        const nameInput = document.getElementById('name') as HTMLInputElement;
        const descriptionTextarea = document.getElementById('description') as HTMLTextAreaElement;
        const iconInput = document.getElementById('icon') as HTMLInputElement;

        if (!nameInput.value || !descriptionTextarea.value || !iconInput.value) {
          Swal.showValidationMessage('กรุณากรอกข้อมูลให้ครบทุกช่อง');
          return false;
        }

        return {
          name: nameInput.value,
          description: descriptionTextarea.value,
          icon: iconInput.value
        };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const newAmenity = {
          id: Math.max(0, ...amenities.map((a) => a.id)) + 1,
          name: result.value.name,
          description: result.value.description,
          icon: result.value.icon,
          createdAt: new Date().toISOString().split("T")[0],
        };

        setAmenities([...amenities, newAmenity]);

        Swal.fire({
          title: "เพิ่มสำเร็จ!",
          text: `เพิ่มสิ่งอำนวยความสะดวก "${newAmenity.name}" เรียบร้อยแล้ว`,
          icon: "success",
          confirmButtonColor: "#10B981",
        });
      }
    });
  };

  const handleOpenEditModal = (amenity: Amenity) => {
    // Use SweetAlert2 directly for the modal
    Swal.fire({
      title: 'แก้ไขสิ่งอำนวยความสะดวก',
      html: `
        <form id="amenityForm" class="text-left">
          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              ชื่อสิ่งอำนวยความสะดวก <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value="${amenity.name}"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อสิ่งอำนวยความสะดวก"
            />
          </div>

          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              คำอธิบาย <span class="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows="3"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="คำอธิบายสิ่งอำนวยความสะดวก"
            >${amenity.description}</textarea>
          </div>

          <div class="mb-4">
            <label for="icon" class="block text-sm font-medium text-gray-700 mb-1">
              ไอคอน <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="icon"
              value="${amenity.icon}"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อไอคอน (เช่น wifi, car, coffee)"
            />
            <p class="mt-1 text-xs text-gray-500">ใส่ชื่อไอคอนที่ต้องการใช้ (เช่น wifi, car, coffee)</p>
          </div>
        </form>
      `,
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: 'บันทึก',
      cancelButtonText: 'ยกเลิก',
      preConfirm: () => {
        const nameInput = document.getElementById('name') as HTMLInputElement;
        const descriptionTextarea = document.getElementById('description') as HTMLTextAreaElement;
        const iconInput = document.getElementById('icon') as HTMLInputElement;

        if (!nameInput.value || !descriptionTextarea.value || !iconInput.value) {
          Swal.showValidationMessage('กรุณากรอกข้อมูลให้ครบทุกช่อง');
          return false;
        }

        return {
          name: nameInput.value,
          description: descriptionTextarea.value,
          icon: iconInput.value
        };
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        const updatedAmenity = {
          ...amenity,
          name: result.value.name,
          description: result.value.description,
          icon: result.value.icon,
        };

        const updatedAmenities = amenities.map((a) =>
          a.id === amenity.id ? updatedAmenity : a
        );

        setAmenities(updatedAmenities);

        Swal.fire({
          title: "อัปเดตสำเร็จ!",
          text: `อัปเดตสิ่งอำนวยความสะดวก "${updatedAmenity.name}" เรียบร้อยแล้ว`,
          icon: "success",
          confirmButtonColor: "#10B981",
        });
      }
    });
  };



  const handleSort = (field: keyof Amenity) => {
    if (sortField === field) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleDeleteAmenity = (id: number, name: string) => {
    Swal.fire({
      title: "ลบสิ่งอำนวยความสะดวก?",
      text: `คุณต้องการลบสิ่งอำนวยความสะดวก "${name}" ใช่หรือไม่?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#EF4444",
      cancelButtonColor: "#6B7280",
      confirmButtonText: "ลบ",
      cancelButtonText: "ยกเลิก",
    }).then((result) => {
      if (result.isConfirmed) {
        setAmenities(amenities.filter((amenity) => amenity.id !== id));

        Swal.fire({
          title: "ลบสำเร็จ!",
          text: `สิ่งอำนวยความสะดวก "${name}" ถูกลบเรียบร้อยแล้ว`,
          icon: "success",
          confirmButtonColor: "#10B981",
        });
      }
    });
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm p-6">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              จัดการสิ่งอำนวยความสะดวก
            </h1>
            <p className="text-sm text-gray-500">
              สร้างและจัดการสิ่งอำนวยความสะดวกสำหรับธุรกิจในระบบ
            </p>
          </div>

          <div className="flex space-x-2">
            {/* Search Bar */}
            <div className="relative">
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                placeholder="ค้นหาสิ่งอำนวยความสะดวก..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            {/* Add Amenity Button */}
            <button
              onClick={handleOpenAddModal}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
            >
              <FiPlus className="mr-2" />
              เพิ่มสิ่งอำนวยความสะดวก
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
            <div className="bg-green-100 p-4 rounded-lg text-green-600">
              <FiTag className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <div className="text-3xl font-bold text-gray-800">{amenities.length}</div>
              <div className="text-sm text-gray-500">สิ่งอำนวยความสะดวกทั้งหมด</div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
            <div className="bg-blue-100 p-4 rounded-lg text-blue-600">
              <FiPackage className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <div className="text-3xl font-bold text-gray-800">
                {amenities.length > 0 ? amenities[0].icon : "-"}
              </div>
              <div className="text-sm text-gray-500">ไอคอนยอดนิยม</div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
            <div className="bg-purple-100 p-4 rounded-lg text-purple-600">
              <FiClock className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <div className="text-3xl font-bold text-gray-800">
                {amenities.length > 0
                  ? new Date(
                      Math.max(
                        ...amenities.map((a) => new Date(a.createdAt).getTime())
                      )
                    ).toLocaleDateString("th-TH")
                  : "-"}
              </div>
              <div className="text-sm text-gray-500">อัปเดตล่าสุด</div>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('id')}
                >
                  รหัส {sortField === 'id' && (
                    <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('name')}
                >
                  ชื่อ {sortField === 'name' && (
                    <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('description')}
                >
                  คำอธิบาย {sortField === 'description' && (
                    <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('icon')}
                >
                  ไอคอน {sortField === 'icon' && (
                    <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('createdAt')}
                >
                  วันที่สร้าง {sortField === 'createdAt' && (
                    <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  การจัดการ
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-10 text-center text-gray-500">
                    <div className="flex justify-center">
                      <svg className="animate-spin h-8 w-8 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                    <p className="mt-2">กำลังโหลดข้อมูล...</p>
                  </td>
                </tr>
              ) : sortedAndFilteredAmenities.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-10 text-center text-gray-500">
                    <p>ไม่พบสิ่งอำนวยความสะดวกที่ตรงกับการค้นหา</p>
                  </td>
                </tr>
              ) : sortedAndFilteredAmenities.map((amenity) => (
                <tr key={amenity.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {amenity.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {amenity.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {amenity.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {amenity.icon}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {amenity.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50"
                        onClick={() => handleOpenEditModal(amenity)}
                      >
                        <FiEdit className="w-5 h-5" />
                      </button>
                      <button
                        className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
                        onClick={() => handleDeleteAmenity(amenity.id, amenity.name)}
                      >
                        <FiTrash2 className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AmenitiesManagement;
