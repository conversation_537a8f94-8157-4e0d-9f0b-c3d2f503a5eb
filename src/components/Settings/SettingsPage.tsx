import { useState } from "react";
import { FiUsers, FiSettings, FiShield } from "react-icons/fi";
import RolePermissions from "./RolePermissions";

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState("roles");

  const tabs = [
    { id: "roles", name: "บทบาทและสิทธิ์", icon: <FiUsers className="w-5 h-5" /> },
    { id: "general", name: "ตั้งค่าทั่วไป", icon: <FiSettings className="w-5 h-5" /> },
    { id: "security", name: "ความปลอดภัย", icon: <FiShield className="w-5 h-5" /> },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">การตั้งค่าระบบ</h1>
        <p className="text-sm text-gray-500">
          จัดการการตั้งค่าและการกำหนดค่าของระบบ 
        </p>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar - Fixed width and position */}
        <div className="w-full md:w-64 md:min-w-[16rem] md:max-w-[16rem] bg-white rounded-lg shadow-sm p-4 h-fit">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? "bg-green-600 text-white"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
              >
                <span className="mr-3">{tab.icon}</span>
                <span className="whitespace-nowrap">{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content - Takes remaining space */}
        <div className="flex-1 overflow-hidden">
          {activeTab === "roles" && <RolePermissions />}

          {activeTab === "general" && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-700 mb-4">ตั้งค่าทั่วไป</h2>
              <p className="text-gray-500">ส่วนนี้อยู่ระหว่างการพัฒนา</p>
            </div>
          )}

          {activeTab === "security" && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-700 mb-4">ความปลอดภัย</h2>
              <p className="text-gray-500">ส่วนนี้อยู่ระหว่างการพัฒนา</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
