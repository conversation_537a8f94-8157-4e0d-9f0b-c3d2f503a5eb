import { useState } from "react";
import { FiEdit, FiSave, <PERSON>X, <PERSON><PERSON><PERSON>ck } from "react-icons/fi";
import Swal from "sweetalert2";

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string[];
}

const RolePermissions = () => {
  // Available permissions
  const allPermissions: Permission[] = [
    // Dashboard permissions
    { id: "view_admin_dashboard", name: "View Admin Dashboard", description: "Access to admin dashboard", category: "Dashboard" },
    { id: "view_store_dashboard", name: "View Store Dashboard", description: "Access to store dashboard", category: "Dashboard" },

    // User management permissions
    { id: "view_users", name: "View Users", description: "View user list", category: "User Management" },
    { id: "create_users", name: "Create Users", description: "Create new users", category: "User Management" },
    { id: "edit_users", name: "Edit Users", description: "Edit existing users", category: "User Management" },
    { id: "delete_users", name: "Delete Users", description: "Delete users", category: "User Management" },

    // Store management permissions
    { id: "view_stores", name: "View Stores", description: "View store list", category: "Store Management" },
    { id: "create_stores", name: "Create Stores", description: "Create new stores", category: "Store Management" },
    { id: "edit_stores", name: "Edit Stores", description: "Edit existing stores", category: "Store Management" },
    { id: "delete_stores", name: "Delete Stores", description: "Delete stores", category: "Store Management" },
    { id: "approve_stores", name: "Approve Stores", description: "Approve store submissions", category: "Store Management" },

    // Content management permissions
    { id: "view_content", name: "View Content", description: "View content", category: "Content Management" },
    { id: "create_content", name: "Create Content", description: "Create new content", category: "Content Management" },
    { id: "edit_content", name: "Edit Content", description: "Edit existing content", category: "Content Management" },
    { id: "delete_content", name: "Delete Content", description: "Delete content", category: "Content Management" },
    { id: "approve_content", name: "Approve Content", description: "Approve content submissions", category: "Content Management" },

    // Category management permissions
    { id: "view_categories", name: "View Categories", description: "View categories", category: "Category Management" },
    { id: "create_categories", name: "Create Categories", description: "Create new categories", category: "Category Management" },
    { id: "edit_categories", name: "Edit Categories", description: "Edit existing categories", category: "Category Management" },
    { id: "delete_categories", name: "Delete Categories", description: "Delete categories", category: "Category Management" },

    // Settings permissions
    { id: "view_settings", name: "View Settings", description: "Access settings", category: "Settings" },
    { id: "edit_settings", name: "Edit Settings", description: "Edit system settings", category: "Settings" },
    { id: "manage_roles", name: "Manage Roles", description: "Manage roles and permissions", category: "Settings" },
  ];

  // Sample role data
  const [roles, setRoles] = useState<Role[]>([
    {
      id: 1,
      name: "Admin",
      description: "Full system access",
      permissions: [
        "view_admin_dashboard", "view_store_dashboard",
        "view_users", "create_users", "edit_users", "delete_users",
        "view_stores", "create_stores", "edit_stores", "delete_stores", "approve_stores",
        "view_content", "create_content", "edit_content", "delete_content", "approve_content",
        "view_categories", "create_categories", "edit_categories", "delete_categories",
        "view_settings", "edit_settings", "manage_roles"
      ],
    },
    {
      id: 2,
      name: "Store Owner",
      description: "Manage own stores and content",
      permissions: [
        "view_store_dashboard",
        "view_stores", "create_stores", "edit_stores",
        "view_content", "create_content", "edit_content", "delete_content",
        "view_categories"
      ],
    },
    {
      id: 3,
      name: "Store Manager",
      description: "Manage assigned stores",
      permissions: [
        "view_store_dashboard",
        "view_stores", "edit_stores",
        "view_content", "create_content", "edit_content",
        "view_categories"
      ],
    },
  ]);

  const [editingRoleId, setEditingRoleId] = useState<number | null>(null);
  const [editedPermissions, setEditedPermissions] = useState<string[]>([]);

  // Group permissions by category
  const permissionsByCategory = allPermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  const handleEditRole = (role: Role) => {
    setEditingRoleId(role.id);
    setEditedPermissions([...role.permissions]);
  };

  const handleSaveRole = (roleId: number) => {
    Swal.fire({
      title: "บันทึกการเปลี่ยนแปลงสิทธิ์?",
      text: "คุณต้องการบันทึกการเปลี่ยนแปลงสิทธิ์ของบทบาทนี้ใช่หรือไม่?",
      icon: "question",
      showCancelButton: true,
      confirmButtonColor: "#10B981",
      cancelButtonColor: "#6B7280",
      confirmButtonText: "บันทึก",
      cancelButtonText: "ยกเลิก",
    }).then((result) => {
      if (result.isConfirmed) {
        const updatedRoles = roles.map((role) => {
          if (role.id === roleId) {
            return { ...role, permissions: editedPermissions };
          }
          return role;
        });

        setRoles(updatedRoles);
        setEditingRoleId(null);

        Swal.fire({
          title: "บันทึกสำเร็จ!",
          text: "การเปลี่ยนแปลงสิทธิ์ถูกบันทึกเรียบร้อยแล้ว",
          icon: "success",
          confirmButtonColor: "#10B981",
        });
      }
    });
  };

  const handleCancelEdit = () => {
    setEditingRoleId(null);
  };

  const handleTogglePermission = (permissionId: string) => {
    if (editedPermissions.includes(permissionId)) {
      setEditedPermissions(editedPermissions.filter((id) => id !== permissionId));
    } else {
      setEditedPermissions([...editedPermissions, permissionId]);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800">
          จัดการบทบาทและสิทธิ์
        </h2>
        <p className="text-sm text-gray-500">
          กำหนดสิทธิ์การเข้าถึงระบบสำหรับแต่ละบทบาท
        </p>
      </div>

        {/* Roles List */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">บทบาทในระบบ</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {roles.map((role) => (
              <div
                key={role.id}
                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-medium text-gray-800">{role.name}</h3>
                  {editingRoleId === role.id ? (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleSaveRole(role.id)}
                        className="text-green-600 hover:text-green-800"
                        title="Save"
                      >
                        <FiSave className="w-5 h-5" />
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="text-red-600 hover:text-red-800"
                        title="Cancel"
                      >
                        <FiX className="w-5 h-5" />
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => handleEditRole(role)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Edit"
                    >
                      <FiEdit className="w-5 h-5" />
                    </button>
                  )}
                </div>
                <p className="text-sm text-gray-500 mb-3">{role.description}</p>
                <div className="text-xs text-gray-500">
                  <span className="font-medium">จำนวนสิทธิ์:</span> {role.permissions.length}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Permissions Editor */}
        {editingRoleId !== null && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-700">
                แก้ไขสิทธิ์: {roles.find((r) => r.id === editingRoleId)?.name}
              </h2>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleSaveRole(editingRoleId)}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  บันทึก
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  ยกเลิก
                </button>
              </div>
            </div>

            <div className="space-y-6">
              {Object.entries(permissionsByCategory).map(([category, permissions]) => (
                <div key={category} className="border-b border-gray-200 pb-4 last:border-0">
                  <h3 className="text-md font-medium text-gray-700 mb-3">{category}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {permissions.map((permission) => (
                      <div
                        key={permission.id}
                        className="flex items-start space-x-2 p-2 rounded-lg hover:bg-gray-100"
                      >
                        <div className="flex-shrink-0 pt-0.5">
                          <input
                            type="checkbox"
                            id={`permission-${permission.id}`}
                            checked={editedPermissions.includes(permission.id)}
                            onChange={() => handleTogglePermission(permission.id)}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          />
                        </div>
                        <div>
                          <label
                            htmlFor={`permission-${permission.id}`}
                            className="text-sm font-medium text-gray-700 cursor-pointer"
                          >
                            {permission.name}
                          </label>
                          <p className="text-xs text-gray-500">{permission.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Permissions Reference */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">รายการสิทธิ์ทั้งหมด</h2>

          <div className="overflow-x-auto max-w-full">
            <table className="w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    หมวดหมู่
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    สิทธิ์
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    คำอธิบาย
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Admin
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Store Owner
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Store Manager
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {allPermissions.map((permission) => (
                  <tr key={permission.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {permission.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {permission.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {permission.description}
                    </td>
                    {roles.map((role) => (
                      <td key={`${permission.id}-${role.id}`} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {role.permissions.includes(permission.id) ? (
                          <FiCheck className="w-5 h-5 text-green-500" />
                        ) : (
                          <FiX className="w-5 h-5 text-red-500" />
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
    </div>
  );
};

export default RolePermissions;
