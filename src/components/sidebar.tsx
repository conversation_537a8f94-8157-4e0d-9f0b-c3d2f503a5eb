import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useUserRole } from "../contexts/UserRoleContext";
import {
  HomeIcon,
  WrenchScrewdriverIcon,
  UserGroupIcon,
  ShoppingBagIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

const Sidebar = () => {
  const location = useLocation();
  const { userRole, setUserRole } = useUserRole();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Define navigation items based on user role
  const getNavItems = () => {
    const commonItems = [
      { name: "แดชบอร์ด", path: "/", icon: <HomeIcon className="w-5 h-5" /> },
      {
        name: "ธุรกิจ",
        path: "/stores",
        icon: <ShoppingBagIcon className="w-5 h-5" />,
      },
    ];

    // Admin-specific items
    if (userRole === "Admin") {
      return [
        ...commonItems,
        {
          name: "การจัดการผู้ใช้",
          path: "/users",
          icon: <UserGroupIcon className="w-5 h-5" />,
        },
        {
          name: "การตั้งค่าธุรกิจ",
          path: "/categories",
          icon: <WrenchScrewdriverIcon className="w-5 h-5" />,
        },
        {
          name: "บันทึกกิจกรรม",
          path: "/activity-log",
          icon: <ClockIcon className="w-5 h-5" />,
        },
      ];
    }

    // Store Owner-specific items
    if (userRole === "Store Owner") {
      return [
        ...commonItems,
        {
          name: "จัดการผู้จัดการร้าน",
          path: "/store-managers",
          icon: <UserGroupIcon className="w-5 h-5" />,
        },
      ];
    }

    // Store Manager-specific items
    return commonItems;
  };

  const navItems = getNavItems();

  return (
    <aside className="w-64 bg-white text-gray-800 h-screen flex flex-col shadow-lg overflow-hidden">
      {/* Logo */}
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex items-center">
          <div className="bg-green-600 rounded-lg p-2 mr-3">
            <img src="/images/logo.png" className="h-6 w-6" alt="" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-800">Tourism CMS</h1>
            <p className="text-xs text-gray-500">Admin Portal</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        <div className="mb-2 text-xs font-semibold uppercase text-gray-500 px-2">
          Main Navigation
        </div>
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.path}>
              <Link
                to={item.path}
                className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                  isActive(item.path)
                    ? "bg-green-600 text-white"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                <span>{item.name}</span>
              </Link>
            </li>
          ))}
        </ul>

        <div className="mt-8 mb-2 text-xs font-semibold uppercase text-gray-500 px-2">
          การตั้งค่า
        </div>
        <ul className="space-y-1">
          <li>
            <Link
              to="/settings"
              className={`flex items-center px-4 py-3 rounded-lg transition-colors ${isActive("/settings") ? "bg-green-600 text-white" : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`}
            >
              <Cog6ToothIcon className="w-5 h-5 mr-3" />
              <span>ตั้งค่า</span>
            </Link>
          </li>
          <li>
            <Link
              to="/help"
              className="flex items-center px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>คำถาม & ช่วยเหลือ</span>
            </Link>
          </li>
        </ul>
      </nav>

      {/* Role Selector */}
      <div className="border-t border-gray-200 p-4">
        <label
          htmlFor="sidebar-role-select"
          className="block text-xs font-medium text-gray-500 mb-2"
        >
          Current Role (Temporary):
        </label>
        <select
          id="sidebar-role-select"
          value={userRole}
          onChange={(e) => setUserRole(e.target.value as any)}
          className="block w-full bg-gray-50 text-gray-700 border border-gray-300 rounded-md py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
        >
          <option value="Admin">Admin</option>
          <option value="Store Owner">Store Owner</option>
          <option value="Store Manager">Store Manager</option>
        </select>
      </div>

      {/* User Profile */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center">
          <div className="bg-gradient-to-r from-green-600 to-green-400 w-10 h-10 rounded-lg flex items-center justify-center mr-3 shadow-lg">
            <UserCircleIcon className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-800">
              {userRole === "Admin"
                ? "Admin User"
                : userRole === "Store Owner"
                ? "Store Owner"
                : "Store Manager"}
            </h3>
            <p className="text-xs text-gray-500">
              {userRole === "Admin"
                ? "Administrator"
                : userRole === "Store Owner"
                ? "Business Owner"
                : "Content Manager"}
            </p>
          </div>
          <button className="p-1.5 rounded-lg hover:bg-gray-100 text-gray-500 hover:text-gray-800 transition-colors">
            <ArrowRightIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
