import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';

// Create a SweetAlert instance with React content
const MySwal = withReactContent(Swal);

// Define the Category type
export interface Category {
  id: number;
  name: string;
  description: string;
  count: number;
  createdAt: string;
}

interface CategoryManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (category: Category) => void;
  category?: Category | null;
  mode: 'add' | 'edit';
}

const CategoryManagementModal: React.FC<CategoryManagementModalProps> = ({
  isOpen,
  onClose,
  onSave,
  category,
  mode
}) => {
  const [formData, setFormData] = useState<Partial<Category>>({
    name: '',
    description: '',
    count: 0
  });
  
  const [errors, setErrors] = useState({
    name: false,
    description: false
  });

  // Update form data when category changes
  useEffect(() => {
    if (category && mode === 'edit') {
      setFormData({
        id: category.id,
        name: category.name,
        description: category.description,
        count: category.count,
        createdAt: category.createdAt
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: '',
        description: '',
        count: 0
      });
    }
  }, [category, mode]);

  // Show the modal when isOpen changes to true
  useEffect(() => {
    if (isOpen) {
      showModal();
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors = {
      name: !formData.name,
      description: !formData.description
    };
    
    setErrors(newErrors);
    return !newErrors.name && !newErrors.description;
  };

  const handleSave = () => {
    if (!validateForm()) {
      return;
    }
    
    // For add mode, generate a new ID
    if (mode === 'add' && !formData.id) {
      formData.id = Math.floor(Math.random() * 10000);
      formData.createdAt = new Date().toISOString().split('T')[0];
    }
    
    // Show confirmation dialog
    Swal.fire({
      title: mode === 'add' ? 'เพิ่มหมวดหมู่ใหม่?' : 'อัปเดตหมวดหมู่?',
      html: `
        <div class="text-left mb-4">
          <p class="font-medium text-gray-700">ชื่อ: ${formData.name}</p>
          <p class="text-sm text-gray-500">คำอธิบาย: ${formData.description}</p>
        </div>
        <p class="mb-4">คุณต้องการ${mode === 'add' ? 'เพิ่ม' : 'อัปเดต'}หมวดหมู่นี้ใช่หรือไม่?</p>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: mode === 'add' ? 'เพิ่ม' : 'อัปเดต',
      cancelButtonText: 'ยกเลิก',
    }).then((result) => {
      if (result.isConfirmed) {
        // Call the onSave callback with the complete category data
        onSave(formData as Category);
        
        // Show success message
        Swal.fire({
          title: mode === 'add' ? 'เพิ่มหมวดหมู่สำเร็จ!' : 'อัปเดตหมวดหมู่สำเร็จ!',
          text: `หมวดหมู่ "${formData.name}" ถูก${mode === 'add' ? 'เพิ่ม' : 'อัปเดต'}เรียบร้อยแล้ว`,
          icon: 'success',
          confirmButtonColor: '#10B981',
        });
        
        // Close the modal
        MySwal.close();
        onClose();
      }
    });
  };

  const showModal = () => {
    MySwal.fire({
      title: mode === 'add' ? 'เพิ่มหมวดหมู่ใหม่' : 'แก้ไขหมวดหมู่',
      html: `
        <form id="categoryForm" class="text-left">
          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              ชื่อหมวดหมู่ <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value="${formData.name || ''}"
              class="w-full border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="ชื่อหมวดหมู่"
            />
            ${errors.name ? '<p class="mt-1 text-xs text-red-500">กรุณากรอกชื่อหมวดหมู่</p>' : ''}
          </div>
          
          <div class="mb-4">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              คำอธิบาย <span class="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              class="w-full border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="คำอธิบายหมวดหมู่"
              rows="3"
            >${formData.description || ''}</textarea>
            ${errors.description ? '<p class="mt-1 text-xs text-red-500">กรุณากรอกคำอธิบาย</p>' : ''}
          </div>
          
          ${mode === 'edit' ? `
          <div class="mb-4">
            <label for="count" class="block text-sm font-medium text-gray-700 mb-1">
              จำนวนธุรกิจ
            </label>
            <input
              type="number"
              id="count"
              value="${formData.count || 0}"
              class="w-full border border-gray-300 rounded-lg p-2 focus:ring-green-500 focus:border-green-500"
              placeholder="จำนวนธุรกิจ"
              min="0"
            />
          </div>
          ` : ''}
        </form>
      `,
      showCancelButton: true,
      confirmButtonColor: '#10B981',
      cancelButtonColor: '#6B7280',
      confirmButtonText: mode === 'add' ? 'เพิ่ม' : 'บันทึก',
      cancelButtonText: 'ยกเลิก',
      didOpen: () => {
        // Add event listeners to form elements
        const nameInput = document.getElementById('name') as HTMLInputElement;
        const descriptionTextarea = document.getElementById('description') as HTMLTextAreaElement;
        const countInput = document.getElementById('count') as HTMLInputElement;
        
        if (nameInput) {
          nameInput.addEventListener('input', (e) => {
            setFormData({
              ...formData,
              name: (e.target as HTMLInputElement).value
            });
            setErrors({
              ...errors,
              name: false
            });
          });
        }
        
        if (descriptionTextarea) {
          descriptionTextarea.addEventListener('input', (e) => {
            setFormData({
              ...formData,
              description: (e.target as HTMLTextAreaElement).value
            });
            setErrors({
              ...errors,
              description: false
            });
          });
        }
        
        if (countInput) {
          countInput.addEventListener('input', (e) => {
            setFormData({
              ...formData,
              count: parseInt((e.target as HTMLInputElement).value) || 0
            });
          });
        }
      },
      preConfirm: () => {
        return validateForm();
      }
    }).then((result) => {
      if (result.isConfirmed) {
        handleSave();
      } else {
        onClose();
      }
    });
  };

  return null; // This component doesn't render anything directly
};

export default CategoryManagementModal;
