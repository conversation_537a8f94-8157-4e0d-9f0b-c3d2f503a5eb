// import React, { useState } from "react";
// import {
//   MagnifyingGlassIcon,
//   BuildingOfficeIcon,
//   ClockIcon,
//   XCircleIcon,
//   AdjustmentsHorizontalIcon,
//   CalendarIcon,
//   ChevronDownIcon,
//   ArrowPathIcon,
// } from "@heroicons/react/24/outline";
// import { CheckCircleIcon } from "@heroicons/react/24/solid";

// const Dashboard = () => {
//   const [startDate, setStartDate] = useState("01-Jan-2024");
//   const [endDate, setEndDate] = useState("30-Jan-2024");
//   const [searchQuery, setSearchQuery] = useState("");
//   const [showFilters, setShowFilters] = useState(true);

//   // Sample ticket data
//   const tickets = [
//     {
//       id: "1998498",
//       title: "เหมาไกว แฮก หน่องลาว",
//       date: "12-Jan-2024 | 08:53 am",
//       createdBy: "<PERSON> doe",
//       status: "active",
//     },
//     {
//       id: "1998499",
//       title: "ร้านเวียดเหนองหนอง",
//       date: "12-Jan-2024 | 08:53 am",
//       createdBy: "John doe",
//       status: "active",
//     },
//     {
//       id: "1998500",
//       title: "ร้านอาหารไทย บางนา",
//       date: "13-Jan-2024 | 09:15 am",
//       createdBy: "Sarah Chen",
//       status: "pending",
//     },
//     {
//       id: "1998501",
//       title: "คาเฟ่สุขุมวิท",
//       date: "14-Jan-2024 | 10:22 am",
//       createdBy: "Mike Johnson",
//       status: "inactive",
//     },
//   ];

//   return (
//     <div className="flex h-screen bg-gray-50 text-gray-800">
//       {/* Main Content */}
//       <div className="flex-1 overflow-auto">
//         <div className="max-w-7xl mx-auto p-6">
//           {/* Page Header */}
//           <div className="flex justify-between items-center mb-6">
//             <div>
//               <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
//               <p className="text-sm text-gray-500">
//                 Overview of business locations and status
//               </p>
//             </div>
//             <div className="flex items-center space-x-3">
//               <button className="p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 text-gray-500">
//                 <ArrowPathIcon className="h-5 w-5" />
//               </button>
//               <button
//                 className="flex items-center px-4 py-2 rounded-lg bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
//                 onClick={() => setShowFilters(!showFilters)}
//               >
//                 <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
//                 <span>Filters</span>
//                 <ChevronDownIcon
//                   className={`h-4 w-4 ml-2 transition-transform ${
//                     showFilters ? "rotate-180" : ""
//                   }`}
//                 />
//               </button>
//             </div>
//           </div>

//           {/* Stats Cards */}
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
//             <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
//               <div className="bg-green-100 p-4 rounded-lg text-green-600">
//                 <BuildingOfficeIcon className="h-6 w-6" />
//               </div>
//               <div className="ml-4">
//                 <div className="text-3xl font-bold text-gray-800">240</div>
//                 <div className="text-sm text-gray-500">ที่ทั้งหมด</div>
//               </div>
//             </div>
//             <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
//               <div className="bg-amber-100 p-4 rounded-lg text-amber-600">
//                 <ClockIcon className="h-6 w-6" />
//               </div>
//               <div className="ml-4">
//                 <div className="text-3xl font-bold text-gray-800">16</div>
//                 <div className="text-sm text-gray-500">กำลังรอ</div>
//               </div>
//             </div>
//             <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex items-center">
//               <div className="bg-red-100 p-4 rounded-lg text-red-600">
//                 <XCircleIcon className="h-6 w-6" />
//               </div>
//               <div className="ml-4">
//                 <div className="text-3xl font-bold text-gray-800">0</div>
//                 <div className="text-sm text-gray-500">พัก/ยกเลิก</div>
//               </div>
//             </div>
//           </div>

//           <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
//             {/* Filter Section */}
//             {showFilters && (
//               <div className="p-6 border-b border-gray-100">
//                 <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
//                   <div className="col-span-1">
//                     <label className="block text-sm font-medium text-gray-700 mb-2">
//                       วันที่เริ่มต้น
//                     </label>
//                     <div className="relative">
//                       <input
//                         type="text"
//                         value={startDate}
//                         onChange={(e) => setStartDate(e.target.value)}
//                         className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
//                       />
//                       <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//                         <CalendarIcon className="h-5 w-5 text-gray-400" />
//                       </div>
//                     </div>
//                   </div>
//                   <div className="col-span-1">
//                     <label className="block text-sm font-medium text-gray-700 mb-2">
//                       วันที่สิ้นสุด
//                     </label>
//                     <div className="relative">
//                       <input
//                         type="text"
//                         value={endDate}
//                         onChange={(e) => setEndDate(e.target.value)}
//                         className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
//                       />
//                       <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//                         <CalendarIcon className="h-5 w-5 text-gray-400" />
//                       </div>
//                     </div>
//                   </div>
//                   <div className="col-span-1">
//                     <label className="block text-sm font-medium text-gray-700 mb-2">
//                       ประเภทสถานที่
//                     </label>
//                     <div className="relative">
//                       <select className="w-full border border-gray-300 rounded-lg p-2 pr-10 text-sm appearance-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all">
//                         <option>ทั้งหมด</option>
//                         <option>ร้านอาหาร</option>
//                         <option>คาเฟ่</option>
//                         <option>โรงแรม</option>
//                       </select>
//                       <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
//                         <ChevronDownIcon className="h-5 w-5 text-gray-400" />
//                       </div>
//                     </div>
//                   </div>
//                   <div className="col-span-1 flex items-end">
//                     <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg w-full text-sm font-medium transition-colors flex items-center justify-center">
//                       <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
//                       ค้นหาข้อมูล
//                     </button>
//                   </div>
//                 </div>
//               </div>
//             )}

//             {/* Search and Table */}
//             <div className="p-6">
//               <div className="relative mb-6">
//                 <input
//                   type="text"
//                   placeholder="ค้นหาด้วยชื่อหรือไอดี"
//                   value={searchQuery}
//                   onChange={(e) => setSearchQuery(e.target.value)}
//                   className="w-full border border-gray-300 rounded-lg p-2 pl-10 text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all"
//                 />
//                 <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//                   <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
//                 </div>
//               </div>

//               {/* Table */}
//               <div className="overflow-x-auto">
//                 <table className="min-w-full divide-y divide-gray-200">
//                   <thead>
//                     <tr className="bg-gray-50">
//                       <th
//                         scope="col"
//                         className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
//                       >
//                         ไอดี
//                       </th>
//                       <th
//                         scope="col"
//                         className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
//                       >
//                         ชื่อธุรกิจ
//                       </th>
//                       <th
//                         scope="col"
//                         className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
//                       >
//                         วันที่บันทึกเข้า
//                       </th>
//                       <th
//                         scope="col"
//                         className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
//                       >
//                         ยืนยันโดย(แอดมิน)
//                       </th>
//                       <th
//                         scope="col"
//                         className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
//                       >
//                         สถานะ
//                       </th>
//                       <th
//                         scope="col"
//                         className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
//                       >
//                         จัดการ
//                       </th>
//                     </tr>
//                   </thead>
//                   <tbody className="bg-white divide-y divide-gray-200">
//                     {tickets.map((ticket) => (
//                       <tr
//                         key={ticket.id}
//                         className="hover:bg-gray-50 transition-colors"
//                       >
//                         <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
//                           #{ticket.id}
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
//                           {ticket.title}
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
//                           {ticket.date}
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
//                           {ticket.createdBy}
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           {ticket.status === "active" && (
//                             <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
//                               <span className="h-2 w-2 rounded-full bg-green-500 mr-2" />
//                               ใช้งาน
//                             </span>
//                           )}
//                           {ticket.status === "pending" && (
//                             <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
//                               <span className="h-2 w-2 rounded-full bg-amber-500 mr-2" />
//                               กำลังรอ
//                             </span>
//                           )}
//                           {ticket.status === "inactive" && (
//                             <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
//                               <span className="h-2 w-2 rounded-full bg-red-500 mr-2" />
//                               พัก/ยกเลิก
//                             </span>
//                           )}
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
//                           <button className="text-green-600 hover:text-green-900 font-medium transition-colors">
//                             View
//                           </button>
//                         </td>
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>

//               {/* Pagination */}
//               <div className="flex items-center justify-between mt-6">
//                 <div className="text-sm text-gray-500">
//                   Showing <span className="font-medium">1</span> to{" "}
//                   <span className="font-medium">4</span> of{" "}
//                   <span className="font-medium">4</span> results
//                 </div>
//                 <div className="flex items-center space-x-2">
//                   <button
//                     className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
//                     disabled
//                   >
//                     Previous
//                   </button>
//                   <button className="px-3 py-1 bg-green-50 border border-green-500 rounded-md text-sm font-medium text-green-600">
//                     1
//                   </button>
//                   <button
//                     className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
//                     disabled
//                   >
//                     Next
//                   </button>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Dashboard;
