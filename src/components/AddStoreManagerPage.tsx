import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUserRole } from "../contexts/UserRoleContext";
import {
  UserIcon,
  EnvelopeIcon,
  LockClosedIcon,
  BuildingStorefrontIcon,
  ShieldCheckIcon,
} from "@heroicons/react/24/outline";

const AddStoreManagerPage: React.FC = () => {
  const navigate = useNavigate();
  const { userRole } = useUserRole();
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    phoneNumber: "",
    selectedStores: [] as string[],
    permissions: {
      canEditContent: true,
      canPublishContent: false,
      canViewAnalytics: true,
      canManageReviews: false,
    },
  });

  // Sample stores owned by the current Store Owner
  // In a real app, these would be fetched from an API
  const ownedStores = [
    { id: "store1", name: "Seaside Tours" },
    { id: "store2", name: "Mountain Adventures" },
  ];

  // Redirect if not a Store Owner
  useEffect(() => {
    if (userRole !== "Store Owner") {
      navigate("/");
    }
  }, [userRole, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData({ ...formData, [id]: value });
  };

  const handleStoreSelection = (storeId: string) => {
    setFormData((prevData) => {
      const selectedStores = [...prevData.selectedStores];
      if (selectedStores.includes(storeId)) {
        return {
          ...prevData,
          selectedStores: selectedStores.filter((id) => id !== storeId),
        };
      } else {
        return {
          ...prevData,
          selectedStores: [...selectedStores, storeId],
        };
      }
    });
  };

  const handlePermissionChange = (permission: string) => {
    setFormData((prevData) => ({
      ...prevData,
      permissions: {
        ...prevData.permissions,
        [permission]: !prevData.permissions[permission as keyof typeof prevData.permissions],
      },
    }));
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    // Validate form
    if (formData.password !== formData.confirmPassword) {
      alert("Passwords do not match!");
      return;
    }
    
    if (formData.selectedStores.length === 0) {
      alert("Please select at least one store to manage");
      return;
    }
    
    // In a real app, you would send this data to your backend
    console.log("Creating Store Manager account:", {
      ...formData,
      role: "Store Manager",
    });
    
    // Show success message and redirect
    alert("Store Manager account created successfully!");
    navigate("/stores");
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-md">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Add Store Manager</h1>
        <p className="text-sm text-gray-500">
          Create a new Store Manager account to help manage your stores
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-medium text-gray-700 mb-4">Personal Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                First Name
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="pl-10 block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="First name"
                  required
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                Last Name
              </label>
              <input
                type="text"
                id="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className="block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Last name"
                required
              />
            </div>
            
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                Username
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  className="pl-10 block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Username for login"
                  required
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                className="block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Contact phone number"
              />
            </div>
            
            <div className="md:col-span-2">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="pl-10 block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Email address"
                  required
                />
              </div>
            </div>
          </div>
        </div>

        {/* Password Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-medium text-gray-700 mb-4">Account Security</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockClosedIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  id="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="pl-10 block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Create a password"
                  required
                  minLength={8}
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Password must be at least 8 characters long
              </p>
            </div>
            
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockClosedIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  id="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="pl-10 block w-full border border-gray-300 rounded-lg p-2.5 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Confirm password"
                  required
                />
              </div>
            </div>
          </div>
        </div>

        {/* Store Assignment Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-medium text-gray-700 mb-4">Store Assignment</h2>
          <p className="text-sm text-gray-500 mb-4">
            Select which of your stores this manager will be able to access and manage
          </p>
          
          <div className="space-y-3">
            {ownedStores.map((store) => (
              <div key={store.id} className="flex items-center">
                <input
                  type="checkbox"
                  id={`store-${store.id}`}
                  checked={formData.selectedStores.includes(store.id)}
                  onChange={() => handleStoreSelection(store.id)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor={`store-${store.id}`} className="ml-3 flex items-center">
                  <BuildingStorefrontIcon className="h-5 w-5 text-gray-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">{store.name}</span>
                </label>
              </div>
            ))}
          </div>
          
          {formData.selectedStores.length === 0 && (
            <p className="mt-2 text-sm text-amber-600">
              Please select at least one store to manage
            </p>
          )}
        </div>

        {/* Permissions Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-medium text-gray-700 mb-4">
            <div className="flex items-center">
              <ShieldCheckIcon className="h-5 w-5 mr-2 text-gray-500" />
              Permissions
            </div>
          </h2>
          <p className="text-sm text-gray-500 mb-4">
            Define what this Store Manager will be able to do
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="canEditContent"
                checked={formData.permissions.canEditContent}
                onChange={() => handlePermissionChange("canEditContent")}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="canEditContent" className="ml-3 text-sm font-medium text-gray-700">
                Edit store content
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="canPublishContent"
                checked={formData.permissions.canPublishContent}
                onChange={() => handlePermissionChange("canPublishContent")}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="canPublishContent" className="ml-3 text-sm font-medium text-gray-700">
                Publish content without approval
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="canViewAnalytics"
                checked={formData.permissions.canViewAnalytics}
                onChange={() => handlePermissionChange("canViewAnalytics")}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="canViewAnalytics" className="ml-3 text-sm font-medium text-gray-700">
                View store analytics
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="canManageReviews"
                checked={formData.permissions.canManageReviews}
                onChange={() => handlePermissionChange("canManageReviews")}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label htmlFor="canManageReviews" className="ml-3 text-sm font-medium text-gray-700">
                Manage customer reviews
              </label>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => navigate("/stores")}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Create Store Manager
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddStoreManagerPage;
