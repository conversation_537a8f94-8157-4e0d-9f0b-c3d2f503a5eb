import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import CategoryManagement from "./CategoryManagement";
import AmenitiesManagement from "./AmenitiesManagement";

const ConfigurationManagement = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"categories" | "amenities">(
    location.pathname === "/amenities" ? "amenities" : "categories"
  );

  const handleTabChange = (tab: "categories" | "amenities") => {
    setActiveTab(tab);
    navigate(tab === "categories" ? "/categories" : "/amenities", { replace: true });
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Tabs */}
      <div className="mb-6 bg-white rounded-lg shadow-sm p-4">
        <div className="flex border-b border-gray-200">
          <button
            className={`px-6 py-3 font-medium text-sm transition-colors duration-200 ${
              activeTab === "categories"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => handleTabChange("categories")}
          >
            หมวดหมู่ธุรกิจ
          </button>
          <button
            className={`px-6 py-3 font-medium text-sm transition-colors duration-200 ${
              activeTab === "amenities"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => handleTabChange("amenities")}
          >
            สิ่งอำนวยความสะดวก
          </button>
        </div>
      </div>

      {/* Content */}
      {activeTab === "categories" ? (
        <CategoryManagement />
      ) : (
        <AmenitiesManagement />
      )}
    </div>
  );
};

export default ConfigurationManagement;
