import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>outer as Router, Routes, Route } from "react-router-dom";
import "./index.css"; // Import custom scrollbar styles
import Sidebar from "./components/sidebar"; // Import Sidebar
// Old dashboard removed
import RoleDashboard from "./components/RoleDashboard"; // New role-based dashboard
import UserManagement from "./components/UserManagement";
import ContentManage from "./components/contentManage";
import CreateAccount from "./components/CreateAccountPage"; // Admin account creation
import AddStoreManager from "./components/AddStoreManagerPage"; // Store Owner's manager creation
import StoreManagers from "./components/StoreManagersPage"; // Store Owner's manager list
import Store from "./components/StorePage"; // Store management component
import SettingsPage from "./components/Settings/SettingsPage";
import ConfigurationManagement from "./components/ConfigurationManagement";
import ActivityLogPage from "./components/ActivityLogPage"; // Activity Log for Admin
import { UserRoleProvider } from "./contexts/UserRoleContext"; // Import UserRoleProvider

const App = () => {
  return (
    <UserRoleProvider>
      <Router>
        <div className="flex">
          <div className="fixed left-0 top-0 h-screen z-10">
            <Sidebar />
          </div>
          <div className="flex-grow p-4 ml-64">
            <Routes>
              <Route path="/" element={<RoleDashboard />} />
              <Route path="/stores" element={<Store />} /> {/* Placeholder */}
              {/* Content management is only accessible through edit buttons */}
              <Route path="/content/edit/:id" element={<ContentManage />} />
              <Route path="/users" element={<UserManagement />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/categories" element={<ConfigurationManagement />} />
              <Route path="/amenities" element={<ConfigurationManagement />} />
              <Route path="/create-account" element={<CreateAccount />} />
              <Route path="/add-store-manager" element={<AddStoreManager />} />
              <Route path="/store-managers" element={<StoreManagers />} />
              <Route path="/activity-log" element={<ActivityLogPage />} />
              {/* Add more routes as needed */}
            </Routes>
          </div>
        </div>
      </Router>
    </UserRoleProvider>
  );
};

export default App;
