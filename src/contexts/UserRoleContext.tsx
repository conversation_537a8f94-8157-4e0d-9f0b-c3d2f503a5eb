import React, { createContext, useState, useContext, ReactNode } from 'react';

// Define user role type
export type UserRole = "Admin" | "Store Owner" | "Store Manager";

// Define context type
type UserRoleContextType = {
  userRole: UserRole;
  setUserRole: (role: UserRole) => void;
};

// Create context with default values
const UserRoleContext = createContext<UserRoleContextType>({
  userRole: "Admin",
  setUserRole: () => {},
});

// Create provider component
export const UserRoleProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [userRole, setUserRole] = useState<UserRole>("Admin");

  return (
    <UserRoleContext.Provider value={{ userRole, setUserRole }}>
      {children}
    </UserRoleContext.Provider>
  );
};

// Create custom hook for using the context
export const useUserRole = () => useContext(UserRoleContext);
