# Tourism CMS Entity-Relationship Diagram

## Core Entities

### USER

- **id** (PK): Unique identifier for each user
- **username**: Login username
- **email**: User's email address
- **password_hash**: Hashed password for security
- **full_name**: User's full name
- **phone_number**: Contact number
- **created_at**: Account creation timestamp
- **updated_at**: Last update timestamp
- **status**: Account status (active/inactive/pending)
- **last_login**: Last login timestamp

### ROLE

- **id** (PK): Unique identifier for each role
- **name**: Role name (Admin/Store Owner/Store Manager)
- **description**: Role description
- **permissions**: JSON object containing detailed permissions
- **can_manage_users**: Boolean flag for user management permission
- **can_manage_content**: Boolean flag for content management permission
- **can_view_analytics**: Boolean flag for analytics viewing permission
- **can_approve_content**: Boolean flag for content approval permission

### STORE

- **id** (PK): Unique identifier for each store
- **title**: Store name
- **business_type**: Type of business (restaurant, cafe, hotel, etc.)
- **short_description**: Brief description for listings
- **description**: Detailed description
- **location**: Physical location/address
- **contact_number**: Business contact number
- **social_media**: Social media handles
- **story_url**: URL to store's story or website
- **reference**: Reference information
- **status**: Store status (active/inactive/pending)
- **owner_id** (FK): Reference to USER who owns this store
- **created_at**: Creation timestamp
- **updated_at**: Last update timestamp

### CONTENT

- **id** (PK): Unique identifier for each content piece
- **store_id** (FK): Reference to STORE this content belongs to
- **title**: Content title
- **description**: Content description
- **content_type**: Type of content (article/listing/promotion)
- **body_text**: Main content text
- **status**: Content status (draft/pending/published/rejected)
- **created_by_id** (FK): Reference to USER who created this content
- **approved_by_id** (FK): Reference to USER who approved this content
- **published_at**: Publication timestamp
- **created_at**: Creation timestamp
- **updated_at**: Last update timestamp

### MEDIA

- **id** (PK): Unique identifier for each media item
- **content_id** (FK): Reference to CONTENT this media belongs to
- **file_path**: Path to the media file
- **file_type**: Type of media (image/video/document)
- **description**: Media description
- **source**: Source attribution
- **alt_text**: Alternative text for accessibility
- **file_size**: Size of the file in bytes
- **created_by_id** (FK): Reference to USER who uploaded this media
- **uploaded_at**: Upload timestamp

## Junction/Relationship Entities

### USER_ROLE

- **user_id** (PK, FK): Reference to USER
- **role_id** (PK, FK): Reference to ROLE
- **assigned_at**: Assignment timestamp
- **assigned_by_id** (FK): Reference to USER who assigned this role

### STORE_MANAGER

- **store_id** (PK, FK): Reference to STORE
- **user_id** (PK, FK): Reference to USER (the manager)
- **assigned_at**: Assignment timestamp
- **assigned_by_id** (FK): Reference to USER who assigned this manager

### CONTENT_APPROVAL

- **content_id** (PK, FK): Reference to CONTENT
- **approved_by_id** (FK): Reference to USER who approved/rejected
- **status**: Approval status (approved/rejected)
- **feedback**: Feedback for rejection or improvement
- **approval_date**: Approval/rejection timestamp

## Analytics and Feedback Entities

### ANALYTICS

- **id** (PK): Unique identifier for analytics record
- **store_id** (FK): Reference to STORE
- **content_id** (FK): Reference to CONTENT
- **views**: Number of views
- **unique_visitors**: Number of unique visitors
- **engagement_time**: Time spent engaging with content
- **metrics**: JSON object with additional metrics
- **recorded_at**: Recording timestamp

### REVIEW

- **id** (PK): Unique identifier for each review
- **store_id** (FK): Reference to STORE being reviewed
- **user_id** (FK): Reference to USER who wrote the review
- **rating**: Numerical rating
- **text**: Review text
- **status**: Review status (pending/approved/rejected)
- **created_at**: Creation timestamp

### COMMENT

- **id** (PK): Unique identifier for each comment
- **content_id** (FK): Reference to CONTENT being commented on
- **user_id** (FK): Reference to USER who wrote the comment
- **text**: Comment text
- **status**: Comment status (pending/approved/rejected)
- **created_at**: Creation timestamp

## Key Relationships

1. **User-Role Relationship**:

   - Users have roles that define their permissions
   - Roles can be assigned to multiple users

2. **Store Ownership and Management**:

   - Stores are owned by Store Owners (Users with Store Owner role)
   - Stores can be managed by multiple Store Managers
   - Store Managers can manage multiple stores

3. **Content Creation and Approval**:

   - Content is created by Users (typically Store Owners or Store Managers)
   - Content belongs to a specific Store
   - Content requires approval (typically by Admins)
   - Content can contain multiple media items

4. **Analytics and Feedback**:
   - Analytics data is collected for Stores and Content
   - Users can write Reviews for Stores
   - Users can write Comments on Content

## Access Control Logic

1. **Admin**:

   - Can view and manage all Stores, Content, Users, and Roles
   - Can approve or reject Content
   - Has access to system-wide analytics

2. **Store Owner**:

   - Can view and manage only their own Stores
   - Can create and manage Store Managers for their Stores
   - Can create and edit Content for their Stores
   - Can view analytics for their Stores

3. **Store Manager**:
   - Can view and manage only Stores they are assigned to
   - Can create and edit Content for assigned Stores
   - Can view limited analytics for assigned Stores
